"""
Attachment manager dialog for viewing and managing email attachments
"""

import os
import shutil
from pathlib import Path
from collections import defaultdict
from typing import List, Dict, Any

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QPushButton, QTreeWidget, QTreeWidgetItem,
                               QGroupBox, QProgressBar, QFileDialog, QMessageBox,
                               QTabWidget, QWidget, QTableWidget, QTableWidgetItem,
                               QHeaderView, QSplitter, QTextEdit, QFrame,
                               QCheckBox, QComboBox, QSpinBox)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont

try:
    from ..core.email_model import EmailModel, EmailAttachment
except ImportError:
    from core.email_model import EmailModel, EmailAttachment


class AttachmentExtractionThread(QThread):
    """Thread for extracting attachments"""
    
    progress_updated = Signal(int, str)
    extraction_completed = Signal(int, int)  # success_count, total_count
    error_occurred = Signal(str)
    
    def __init__(self, emails: List[EmailModel], output_dir: str, selected_types: List[str] = None):
        super().__init__()
        self.emails = emails
        self.output_dir = Path(output_dir)
        self.selected_types = selected_types or []
        self.should_stop = False
    
    def run(self):
        """Extract attachments from emails"""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            
            total_attachments = 0
            extracted_count = 0
            
            # Count total attachments
            for email in self.emails:
                if self.should_stop:
                    return
                for attachment in email.attachments:
                    if self._should_extract_attachment(attachment):
                        total_attachments += 1
            
            if total_attachments == 0:
                self.extraction_completed.emit(0, 0)
                return
            
            # Extract attachments
            for i, email in enumerate(self.emails):
                if self.should_stop:
                    return
                
                self.progress_updated.emit(
                    int((i / len(self.emails)) * 100),
                    f"Processing email {i+1} of {len(self.emails)}"
                )
                
                for attachment in email.attachments:
                    if self.should_stop:
                        return
                    
                    if self._should_extract_attachment(attachment):
                        try:
                            self._extract_attachment(email, attachment)
                            extracted_count += 1
                        except Exception as e:
                            print(f"Failed to extract {attachment.filename}: {e}")
            
            self.extraction_completed.emit(extracted_count, total_attachments)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def _should_extract_attachment(self, attachment: EmailAttachment) -> bool:
        """Check if attachment should be extracted"""
        if not self.selected_types:
            return True
        
        file_ext = Path(attachment.filename).suffix.lower()
        return file_ext in self.selected_types
    
    def _extract_attachment(self, email: EmailModel, attachment: EmailAttachment):
        """Extract single attachment"""
        # Create safe filename
        safe_filename = self._make_safe_filename(attachment.filename)
        
        # Create email-specific subdirectory
        email_dir = self.output_dir / f"email_{email.message_id[:10]}"
        email_dir.mkdir(exist_ok=True)
        
        output_path = email_dir / safe_filename
        
        # Handle duplicate filenames
        counter = 1
        original_path = output_path
        while output_path.exists():
            stem = original_path.stem
            suffix = original_path.suffix
            output_path = original_path.parent / f"{stem}_{counter}{suffix}"
            counter += 1
        
        # Write attachment data
        with open(output_path, 'wb') as f:
            f.write(attachment.data)
    
    def _make_safe_filename(self, filename: str) -> str:
        """Make filename safe for filesystem"""
        # Replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        safe_name = filename
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # Limit length
        if len(safe_name) > 200:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:200-len(ext)] + ext
        
        return safe_name
    
    def stop(self):
        """Stop extraction"""
        self.should_stop = True


class AttachmentManagerDialog(QDialog):
    """Dialog for managing email attachments"""
    
    def __init__(self, emails: List[EmailModel], parent=None):
        super().__init__(parent)
        self.emails = emails
        self.attachment_stats = {}
        self.extraction_thread = None
        
        self.setWindowTitle("Attachment Manager")
        self.setModal(True)
        self.resize(800, 600)
        
        self._analyze_attachments()
        self._setup_ui()
        self._setup_connections()
        self._populate_data()
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Overview tab
        self._create_overview_tab()
        
        # File types tab
        self._create_file_types_tab()
        
        # Extraction tab
        self._create_extraction_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.close_button = QPushButton("Close")
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def _create_overview_tab(self):
        """Create overview tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Statistics
        stats_group = QGroupBox("Attachment Statistics")
        stats_layout = QGridLayout(stats_group)
        
        total_emails = len(self.emails)
        emails_with_attachments = len([e for e in self.emails if e.has_attachments])
        total_attachments = sum(len(e.attachments) for e in self.emails)
        total_size = sum(sum(att.size for att in e.attachments) for e in self.emails)
        
        stats_layout.addWidget(QLabel("Total Emails:"), 0, 0)
        stats_layout.addWidget(QLabel(str(total_emails)), 0, 1)
        
        stats_layout.addWidget(QLabel("Emails with Attachments:"), 1, 0)
        stats_layout.addWidget(QLabel(f"{emails_with_attachments} ({emails_with_attachments/total_emails*100:.1f}%)"), 1, 1)
        
        stats_layout.addWidget(QLabel("Total Attachments:"), 2, 0)
        stats_layout.addWidget(QLabel(str(total_attachments)), 2, 1)
        
        stats_layout.addWidget(QLabel("Total Size:"), 3, 0)
        stats_layout.addWidget(QLabel(self._format_size(total_size)), 3, 1)
        
        layout.addWidget(stats_group)
        
        # Attachment list
        list_group = QGroupBox("All Attachments")
        list_layout = QVBoxLayout(list_group)
        
        self.attachment_tree = QTreeWidget()
        self.attachment_tree.setHeaderLabels(["Email Subject", "Filename", "Type", "Size"])
        self.attachment_tree.header().setSectionResizeMode(QHeaderView.ResizeToContents)
        
        list_layout.addWidget(self.attachment_tree)
        layout.addWidget(list_group)
        
        self.tab_widget.addTab(tab, "Overview")
    
    def _create_file_types_tab(self):
        """Create file types analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # File type statistics
        self.file_types_table = QTableWidget()
        self.file_types_table.setColumnCount(4)
        self.file_types_table.setHorizontalHeaderLabels(["File Type", "Count", "Total Size", "Average Size"])
        self.file_types_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(QLabel("File Types Analysis:"))
        layout.addWidget(self.file_types_table)
        
        self.tab_widget.addTab(tab, "File Types")
    
    def _create_extraction_tab(self):
        """Create bulk extraction tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Extraction options
        options_group = QGroupBox("Extraction Options")
        options_layout = QVBoxLayout(options_group)
        
        # Output directory
        dir_layout = QHBoxLayout()
        self.output_dir_input = QLineEdit()
        self.output_dir_input.setPlaceholderText("Select output directory...")
        self.browse_button = QPushButton("Browse...")
        
        dir_layout.addWidget(QLabel("Output Directory:"))
        dir_layout.addWidget(self.output_dir_input)
        dir_layout.addWidget(self.browse_button)
        options_layout.addLayout(dir_layout)
        
        # File type filter
        filter_layout = QHBoxLayout()
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "All Files",
            "Images (.jpg, .png, .gif, .bmp)",
            "Documents (.pdf, .doc, .docx, .txt)",
            "Spreadsheets (.xls, .xlsx, .csv)",
            "Archives (.zip, .rar, .7z)",
            "Custom..."
        ])
        
        filter_layout.addWidget(QLabel("File Types:"))
        filter_layout.addWidget(self.filter_combo)
        options_layout.addLayout(filter_layout)
        
        # Custom filter input
        self.custom_filter_input = QLineEdit()
        self.custom_filter_input.setPlaceholderText("e.g., .pdf,.jpg,.doc")
        self.custom_filter_input.setVisible(False)
        options_layout.addWidget(self.custom_filter_input)
        
        layout.addWidget(options_group)
        
        # Progress
        progress_group = QGroupBox("Extraction Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("Ready to extract")
        
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(progress_group)
        
        # Extract button
        self.extract_button = QPushButton("Extract Attachments")
        self.extract_button.setEnabled(False)
        layout.addWidget(self.extract_button)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Bulk Extract")
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.close_button.clicked.connect(self.accept)
        self.browse_button.clicked.connect(self._browse_output_directory)
        self.extract_button.clicked.connect(self._start_extraction)
        self.filter_combo.currentTextChanged.connect(self._on_filter_changed)
        self.output_dir_input.textChanged.connect(self._update_extract_button)
    
    def _analyze_attachments(self):
        """Analyze attachments to gather statistics"""
        self.attachment_stats = defaultdict(lambda: {'count': 0, 'total_size': 0, 'files': []})
        
        for email in self.emails:
            for attachment in email.attachments:
                file_ext = Path(attachment.filename).suffix.lower() or '.unknown'
                self.attachment_stats[file_ext]['count'] += 1
                self.attachment_stats[file_ext]['total_size'] += attachment.size
                self.attachment_stats[file_ext]['files'].append((email, attachment))
    
    def _populate_data(self):
        """Populate UI with attachment data"""
        # Populate attachment tree
        for email in self.emails:
            if not email.attachments:
                continue
            
            email_item = QTreeWidgetItem([email.subject[:50] + "..." if len(email.subject) > 50 else email.subject])
            email_item.setFont(0, QFont("", -1, QFont.Bold))
            
            for attachment in email.attachments:
                att_item = QTreeWidgetItem([
                    "",  # Email subject (empty for attachment items)
                    attachment.filename,
                    Path(attachment.filename).suffix.upper() or "Unknown",
                    self._format_size(attachment.size)
                ])
                email_item.addChild(att_item)
            
            self.attachment_tree.addTopLevelItem(email_item)
        
        self.attachment_tree.expandAll()
        
        # Populate file types table
        sorted_types = sorted(self.attachment_stats.items(), 
                            key=lambda x: x[1]['count'], reverse=True)
        
        self.file_types_table.setRowCount(len(sorted_types))
        
        for i, (file_type, stats) in enumerate(sorted_types):
            self.file_types_table.setItem(i, 0, QTableWidgetItem(file_type))
            self.file_types_table.setItem(i, 1, QTableWidgetItem(str(stats['count'])))
            self.file_types_table.setItem(i, 2, QTableWidgetItem(self._format_size(stats['total_size'])))
            
            avg_size = stats['total_size'] / stats['count'] if stats['count'] > 0 else 0
            self.file_types_table.setItem(i, 3, QTableWidgetItem(self._format_size(avg_size)))
    
    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        
        return f"{size_bytes:.1f} TB"
    
    def _browse_output_directory(self):
        """Browse for output directory"""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Output Directory", 
            self.output_dir_input.text() or str(Path.home())
        )
        
        if directory:
            self.output_dir_input.setText(directory)
    
    def _on_filter_changed(self):
        """Handle filter combo change"""
        is_custom = self.filter_combo.currentText() == "Custom..."
        self.custom_filter_input.setVisible(is_custom)
    
    def _update_extract_button(self):
        """Update extract button state"""
        has_output_dir = bool(self.output_dir_input.text().strip())
        has_attachments = any(email.has_attachments for email in self.emails)
        
        self.extract_button.setEnabled(has_output_dir and has_attachments)
    
    def _start_extraction(self):
        """Start attachment extraction"""
        output_dir = self.output_dir_input.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "Error", "Please select an output directory.")
            return
        
        # Get selected file types
        selected_types = self._get_selected_file_types()
        
        # Start extraction thread
        self.extraction_thread = AttachmentExtractionThread(
            self.emails, output_dir, selected_types
        )
        
        self.extraction_thread.progress_updated.connect(self._on_extraction_progress)
        self.extraction_thread.extraction_completed.connect(self._on_extraction_completed)
        self.extraction_thread.error_occurred.connect(self._on_extraction_error)
        
        self.extract_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("Starting extraction...")
        
        self.extraction_thread.start()
    
    def _get_selected_file_types(self) -> List[str]:
        """Get selected file types for filtering"""
        filter_text = self.filter_combo.currentText()
        
        if filter_text == "All Files":
            return []
        elif filter_text == "Images (.jpg, .png, .gif, .bmp)":
            return ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
        elif filter_text == "Documents (.pdf, .doc, .docx, .txt)":
            return ['.pdf', '.doc', '.docx', '.txt', '.rtf']
        elif filter_text == "Spreadsheets (.xls, .xlsx, .csv)":
            return ['.xls', '.xlsx', '.csv']
        elif filter_text == "Archives (.zip, .rar, .7z)":
            return ['.zip', '.rar', '.7z', '.tar', '.gz']
        elif filter_text == "Custom...":
            custom_text = self.custom_filter_input.text().strip()
            if custom_text:
                return [ext.strip() for ext in custom_text.split(',')]
        
        return []
    
    def _on_extraction_progress(self, progress: int, message: str):
        """Handle extraction progress update"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)
    
    def _on_extraction_completed(self, success_count: int, total_count: int):
        """Handle extraction completion"""
        self.progress_bar.setValue(100)
        self.progress_label.setText(f"Extraction completed: {success_count}/{total_count} files")
        self.extract_button.setEnabled(True)
        
        QMessageBox.information(
            self, "Extraction Complete",
            f"Successfully extracted {success_count} of {total_count} attachments."
        )
    
    def _on_extraction_error(self, error_message: str):
        """Handle extraction error"""
        self.progress_label.setText("Extraction failed")
        self.extract_button.setEnabled(True)
        
        QMessageBox.critical(self, "Extraction Error", f"Extraction failed: {error_message}")
    
    def closeEvent(self, event):
        """Handle dialog close"""
        if self.extraction_thread and self.extraction_thread.isRunning():
            self.extraction_thread.stop()
            self.extraction_thread.wait(3000)  # Wait up to 3 seconds
        
        event.accept()
