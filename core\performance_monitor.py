"""
Performance monitoring and optimization for the mbox reader
"""

import time
import threading
import psutil
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from collections import deque
import statistics


@dataclass
class PerformanceMetric:
    """Single performance metric data point"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    operation: str
    duration_ms: Optional[float] = None


class PerformanceMonitor:
    """Monitor application performance and resource usage"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history = deque(maxlen=max_history)
        self.operation_times = {}  # operation_name -> list of durations
        self.lock = threading.RLock()
        self.process = psutil.Process(os.getpid())
        
        # Performance thresholds
        self.thresholds = {
            'cpu_percent_warning': 80.0,
            'memory_mb_warning': 1000.0,
            'operation_time_warning_ms': 5000.0
        }
    
    def record_metric(self, operation: str, duration_ms: Optional[float] = None):
        """Record a performance metric"""
        with self.lock:
            try:
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / (1024 * 1024)
                
                metric = PerformanceMetric(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_mb=memory_mb,
                    operation=operation,
                    duration_ms=duration_ms
                )
                
                self.metrics_history.append(metric)
                
                # Track operation times
                if duration_ms is not None:
                    if operation not in self.operation_times:
                        self.operation_times[operation] = deque(maxlen=100)
                    self.operation_times[operation].append(duration_ms)
                
            except Exception as e:
                print(f"Error recording performance metric: {e}")
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        with self.lock:
            try:
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / (1024 * 1024)
                
                return {
                    'cpu_percent': round(cpu_percent, 2),
                    'memory_mb': round(memory_mb, 2),
                    'memory_percent': round(self.process.memory_percent(), 2),
                    'num_threads': self.process.num_threads(),
                    'open_files': len(self.process.open_files()) if hasattr(self.process, 'open_files') else 0
                }
            except Exception as e:
                print(f"Error getting current stats: {e}")
                return {}
    
    def get_operation_stats(self, operation: str) -> Dict[str, Any]:
        """Get statistics for a specific operation"""
        with self.lock:
            if operation not in self.operation_times:
                return {}
            
            times = list(self.operation_times[operation])
            if not times:
                return {}
            
            return {
                'count': len(times),
                'avg_ms': round(statistics.mean(times), 2),
                'min_ms': round(min(times), 2),
                'max_ms': round(max(times), 2),
                'median_ms': round(statistics.median(times), 2),
                'std_dev_ms': round(statistics.stdev(times) if len(times) > 1 else 0, 2)
            }
    
    def get_all_operation_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all operations"""
        with self.lock:
            stats = {}
            for operation in self.operation_times.keys():
                stats[operation] = self.get_operation_stats(operation)
            return stats
    
    def get_performance_warnings(self) -> List[str]:
        """Get list of current performance warnings"""
        warnings = []
        
        try:
            current_stats = self.get_current_stats()
            
            # CPU warning
            if current_stats.get('cpu_percent', 0) > self.thresholds['cpu_percent_warning']:
                warnings.append(f"High CPU usage: {current_stats['cpu_percent']}%")
            
            # Memory warning
            if current_stats.get('memory_mb', 0) > self.thresholds['memory_mb_warning']:
                warnings.append(f"High memory usage: {current_stats['memory_mb']:.1f} MB")
            
            # Operation time warnings
            for operation, stats in self.get_all_operation_stats().items():
                if stats.get('avg_ms', 0) > self.thresholds['operation_time_warning_ms']:
                    warnings.append(f"Slow operation '{operation}': {stats['avg_ms']:.1f}ms average")
        
        except Exception as e:
            warnings.append(f"Error checking performance: {e}")
        
        return warnings
    
    def get_memory_usage_trend(self, minutes: int = 5) -> List[float]:
        """Get memory usage trend over the last N minutes"""
        with self.lock:
            cutoff_time = time.time() - (minutes * 60)
            recent_metrics = [
                m for m in self.metrics_history 
                if m.timestamp >= cutoff_time
            ]
            
            return [m.memory_mb for m in recent_metrics]
    
    def clear_history(self):
        """Clear performance history"""
        with self.lock:
            self.metrics_history.clear()
            self.operation_times.clear()


class OperationTimer:
    """Context manager for timing operations"""
    
    def __init__(self, monitor: PerformanceMonitor, operation_name: str):
        self.monitor = monitor
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000
            self.monitor.record_metric(self.operation_name, duration_ms)


class MemoryProfiler:
    """Profile memory usage of specific operations"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.snapshots = {}
    
    def take_snapshot(self, name: str):
        """Take a memory snapshot"""
        try:
            memory_info = self.process.memory_info()
            self.snapshots[name] = {
                'timestamp': time.time(),
                'rss_mb': memory_info.rss / (1024 * 1024),
                'vms_mb': memory_info.vms / (1024 * 1024)
            }
        except Exception as e:
            print(f"Error taking memory snapshot: {e}")
    
    def compare_snapshots(self, before: str, after: str) -> Dict[str, Any]:
        """Compare two memory snapshots"""
        if before not in self.snapshots or after not in self.snapshots:
            return {}
        
        before_snap = self.snapshots[before]
        after_snap = self.snapshots[after]
        
        return {
            'rss_diff_mb': round(after_snap['rss_mb'] - before_snap['rss_mb'], 2),
            'vms_diff_mb': round(after_snap['vms_mb'] - before_snap['vms_mb'], 2),
            'time_diff_s': round(after_snap['timestamp'] - before_snap['timestamp'], 2)
        }
    
    def get_current_memory(self) -> Dict[str, float]:
        """Get current memory usage"""
        try:
            memory_info = self.process.memory_info()
            return {
                'rss_mb': round(memory_info.rss / (1024 * 1024), 2),
                'vms_mb': round(memory_info.vms / (1024 * 1024), 2),
                'percent': round(self.process.memory_percent(), 2)
            }
        except Exception as e:
            print(f"Error getting current memory: {e}")
            return {}


class PerformanceOptimizer:
    """Automatic performance optimization suggestions"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
    
    def analyze_performance(self) -> Dict[str, Any]:
        """Analyze current performance and provide recommendations"""
        analysis = {
            'current_stats': self.monitor.get_current_stats(),
            'warnings': self.monitor.get_performance_warnings(),
            'recommendations': []
        }
        
        # Analyze memory usage
        current_memory = analysis['current_stats'].get('memory_mb', 0)
        if current_memory > 500:
            analysis['recommendations'].append({
                'type': 'memory',
                'message': 'High memory usage detected. Consider clearing caches or reducing email preview limit.',
                'action': 'clear_cache'
            })
        
        # Analyze operation performance
        operation_stats = self.monitor.get_all_operation_stats()
        for operation, stats in operation_stats.items():
            if stats.get('avg_ms', 0) > 2000:  # 2 seconds
                analysis['recommendations'].append({
                    'type': 'performance',
                    'message': f"Operation '{operation}' is slow (avg: {stats['avg_ms']:.1f}ms). Consider optimization.",
                    'action': 'optimize_operation',
                    'operation': operation
                })
        
        # Analyze CPU usage
        cpu_percent = analysis['current_stats'].get('cpu_percent', 0)
        if cpu_percent > 70:
            analysis['recommendations'].append({
                'type': 'cpu',
                'message': 'High CPU usage detected. Consider reducing concurrent operations.',
                'action': 'reduce_concurrency'
            })
        
        return analysis
    
    def get_optimization_suggestions(self) -> List[str]:
        """Get list of optimization suggestions"""
        analysis = self.analyze_performance()
        return [rec['message'] for rec in analysis['recommendations']]


# Global performance monitor instance
_global_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def time_operation(operation_name: str):
    """Decorator for timing operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            with OperationTimer(monitor, operation_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator
