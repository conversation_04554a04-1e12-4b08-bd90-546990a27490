"""
Email tagging system for organizing and categorizing emails
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from .email_model import EmailModel


class TagColor(Enum):
    """Predefined tag colors"""
    RED = "#e74c3c"
    BLUE = "#3498db"
    GREEN = "#27ae60"
    YELLOW = "#f1c40f"
    PURPLE = "#9b59b6"
    ORANGE = "#e67e22"
    PINK = "#e91e63"
    TEAL = "#1abc9c"
    GRAY = "#95a5a6"
    BROWN = "#8d6e63"


@dataclass
class EmailTag:
    """Email tag definition"""
    name: str
    color: str = TagColor.BLUE.value
    description: str = ""
    created_date: str = ""
    auto_rules: List[Dict] = None
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if self.auto_rules is None:
            self.auto_rules = []


@dataclass
class AutoTagRule:
    """Automatic tagging rule"""
    field: str  # 'subject', 'sender', 'recipients', 'content'
    pattern: str  # Text pattern or regex
    is_regex: bool = False
    case_sensitive: bool = False
    tag_name: str = ""
    
    def matches(self, email: EmailModel) -> bool:
        """Check if email matches this rule"""
        field_value = self._get_field_value(email)
        if not field_value:
            return False
        
        if self.is_regex:
            try:
                flags = 0 if self.case_sensitive else re.IGNORECASE
                return bool(re.search(self.pattern, field_value, flags))
            except re.error:
                return False
        else:
            search_text = field_value if self.case_sensitive else field_value.lower()
            pattern_text = self.pattern if self.case_sensitive else self.pattern.lower()
            return pattern_text in search_text
    
    def _get_field_value(self, email: EmailModel) -> str:
        """Get field value from email"""
        if self.field == 'subject':
            return email.subject
        elif self.field == 'sender':
            return email.sender
        elif self.field == 'recipients':
            return f"{email.recipients} {email.cc} {email.bcc}"
        elif self.field == 'content':
            return f"{email.text_content} {email.html_content}"
        else:
            return ""


class TagManager:
    """Manages email tags and tagging operations"""
    
    def __init__(self, config_dir: Path):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.tags_file = self.config_dir / "tags.json"
        self.email_tags_file = self.config_dir / "email_tags.json"
        
        # Tag definitions
        self.tags: Dict[str, EmailTag] = {}
        
        # Email to tags mapping (email_id -> set of tag names)
        self.email_tags: Dict[str, Set[str]] = {}
        
        # Auto-tagging rules
        self.auto_rules: List[AutoTagRule] = []
        
        self._load_data()
        self._create_default_tags()
    
    def _load_data(self):
        """Load tags and email mappings from files"""
        # Load tag definitions
        if self.tags_file.exists():
            try:
                with open(self.tags_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for tag_data in data.get('tags', []):
                        tag = EmailTag(**tag_data)
                        self.tags[tag.name] = tag
                    
                    # Load auto rules
                    for rule_data in data.get('auto_rules', []):
                        rule = AutoTagRule(**rule_data)
                        self.auto_rules.append(rule)
            except Exception as e:
                print(f"Error loading tags: {e}")
        
        # Load email tag mappings
        if self.email_tags_file.exists():
            try:
                with open(self.email_tags_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for email_id, tag_list in data.items():
                        self.email_tags[email_id] = set(tag_list)
            except Exception as e:
                print(f"Error loading email tags: {e}")
    
    def _save_data(self):
        """Save tags and email mappings to files"""
        try:
            # Save tag definitions and auto rules
            tags_data = {
                'tags': [asdict(tag) for tag in self.tags.values()],
                'auto_rules': [asdict(rule) for rule in self.auto_rules]
            }
            
            with open(self.tags_file, 'w', encoding='utf-8') as f:
                json.dump(tags_data, f, indent=2, ensure_ascii=False)
            
            # Save email tag mappings
            email_tags_data = {
                email_id: list(tags) for email_id, tags in self.email_tags.items()
            }
            
            with open(self.email_tags_file, 'w', encoding='utf-8') as f:
                json.dump(email_tags_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error saving tags: {e}")
    
    def _create_default_tags(self):
        """Create default tags if none exist"""
        if not self.tags:
            default_tags = [
                EmailTag("Important", TagColor.RED.value, "High priority emails"),
                EmailTag("Work", TagColor.BLUE.value, "Work-related emails"),
                EmailTag("Personal", TagColor.GREEN.value, "Personal emails"),
                EmailTag("Newsletter", TagColor.YELLOW.value, "Newsletters and subscriptions"),
                EmailTag("Spam", TagColor.GRAY.value, "Spam or unwanted emails"),
                EmailTag("Archive", TagColor.BROWN.value, "Archived emails"),
            ]
            
            for tag in default_tags:
                self.tags[tag.name] = tag
            
            self._save_data()
    
    def create_tag(self, name: str, color: str = TagColor.BLUE.value, description: str = "") -> EmailTag:
        """Create a new tag"""
        if name in self.tags:
            raise ValueError(f"Tag '{name}' already exists")
        
        tag = EmailTag(name, color, description)
        self.tags[name] = tag
        self._save_data()
        return tag
    
    def delete_tag(self, name: str):
        """Delete a tag and remove it from all emails"""
        if name not in self.tags:
            raise ValueError(f"Tag '{name}' does not exist")
        
        # Remove tag from all emails
        for email_id in list(self.email_tags.keys()):
            self.email_tags[email_id].discard(name)
            if not self.email_tags[email_id]:
                del self.email_tags[email_id]
        
        # Remove auto rules for this tag
        self.auto_rules = [rule for rule in self.auto_rules if rule.tag_name != name]
        
        # Delete tag
        del self.tags[name]
        self._save_data()
    
    def rename_tag(self, old_name: str, new_name: str):
        """Rename a tag"""
        if old_name not in self.tags:
            raise ValueError(f"Tag '{old_name}' does not exist")
        if new_name in self.tags:
            raise ValueError(f"Tag '{new_name}' already exists")
        
        # Update tag definition
        tag = self.tags[old_name]
        tag.name = new_name
        self.tags[new_name] = tag
        del self.tags[old_name]
        
        # Update email mappings
        for email_id, tags in self.email_tags.items():
            if old_name in tags:
                tags.remove(old_name)
                tags.add(new_name)
        
        # Update auto rules
        for rule in self.auto_rules:
            if rule.tag_name == old_name:
                rule.tag_name = new_name
        
        self._save_data()
    
    def get_tags(self) -> List[EmailTag]:
        """Get all tags"""
        return list(self.tags.values())
    
    def get_tag(self, name: str) -> Optional[EmailTag]:
        """Get a specific tag"""
        return self.tags.get(name)
    
    def add_tag_to_email(self, email: EmailModel, tag_name: str):
        """Add a tag to an email"""
        if tag_name not in self.tags:
            raise ValueError(f"Tag '{tag_name}' does not exist")
        
        email_id = email.message_id
        if email_id not in self.email_tags:
            self.email_tags[email_id] = set()
        
        self.email_tags[email_id].add(tag_name)
        self._save_data()
    
    def remove_tag_from_email(self, email: EmailModel, tag_name: str):
        """Remove a tag from an email"""
        email_id = email.message_id
        if email_id in self.email_tags:
            self.email_tags[email_id].discard(tag_name)
            if not self.email_tags[email_id]:
                del self.email_tags[email_id]
            self._save_data()
    
    def get_email_tags(self, email: EmailModel) -> Set[str]:
        """Get tags for an email"""
        return self.email_tags.get(email.message_id, set())
    
    def get_emails_with_tag(self, emails: List[EmailModel], tag_name: str) -> List[EmailModel]:
        """Get all emails with a specific tag"""
        return [email for email in emails if tag_name in self.get_email_tags(email)]
    
    def get_untagged_emails(self, emails: List[EmailModel]) -> List[EmailModel]:
        """Get all emails without any tags"""
        return [email for email in emails if not self.get_email_tags(email)]
    
    def add_auto_rule(self, rule: AutoTagRule):
        """Add an automatic tagging rule"""
        if rule.tag_name not in self.tags:
            raise ValueError(f"Tag '{rule.tag_name}' does not exist")
        
        self.auto_rules.append(rule)
        self._save_data()
    
    def remove_auto_rule(self, rule: AutoTagRule):
        """Remove an automatic tagging rule"""
        if rule in self.auto_rules:
            self.auto_rules.remove(rule)
            self._save_data()
    
    def get_auto_rules(self) -> List[AutoTagRule]:
        """Get all auto-tagging rules"""
        return self.auto_rules.copy()
    
    def apply_auto_tags(self, emails: List[EmailModel]) -> int:
        """Apply automatic tagging rules to emails"""
        tagged_count = 0
        
        for email in emails:
            for rule in self.auto_rules:
                if rule.matches(email):
                    if rule.tag_name not in self.get_email_tags(email):
                        self.add_tag_to_email(email, rule.tag_name)
                        tagged_count += 1
        
        return tagged_count
    
    def get_tag_statistics(self, emails: List[EmailModel]) -> Dict[str, int]:
        """Get statistics about tag usage"""
        stats = {}
        
        for tag_name in self.tags:
            count = len(self.get_emails_with_tag(emails, tag_name))
            stats[tag_name] = count
        
        # Add untagged count
        stats['_untagged'] = len(self.get_untagged_emails(emails))
        
        return stats
    
    def search_emails_by_tags(self, emails: List[EmailModel], 
                            include_tags: List[str] = None,
                            exclude_tags: List[str] = None) -> List[EmailModel]:
        """Search emails by tag criteria"""
        if not include_tags and not exclude_tags:
            return emails
        
        filtered = []
        
        for email in emails:
            email_tags = self.get_email_tags(email)
            
            # Check include criteria
            if include_tags:
                if not any(tag in email_tags for tag in include_tags):
                    continue
            
            # Check exclude criteria
            if exclude_tags:
                if any(tag in email_tags for tag in exclude_tags):
                    continue
            
            filtered.append(email)
        
        return filtered

    def export_tags(self, filepath: Path):
        """Export tags to a file"""
        data = {
            'tags': [asdict(tag) for tag in self.tags.values()],
            'auto_rules': [asdict(rule) for rule in self.auto_rules],
            'exported_date': datetime.now().isoformat()
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def import_tags(self, filepath: Path, merge: bool = True):
        """Import tags from a file"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if not merge:
            self.tags.clear()
            self.auto_rules.clear()

        # Import tags
        for tag_data in data.get('tags', []):
            tag = EmailTag(**tag_data)
            self.tags[tag.name] = tag

        # Import auto rules
        for rule_data in data.get('auto_rules', []):
            rule = AutoTagRule(**rule_data)
            if rule not in self.auto_rules:
                self.auto_rules.append(rule)

        self._save_data()
