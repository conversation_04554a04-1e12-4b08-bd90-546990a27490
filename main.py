#!/usr/bin/env python3
"""
Advanced Mbox Reader/Editor Tool
Modern GUI application for reading and editing mbox files with PySide6
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# Hide console window on Windows
if sys.platform == "win32":
    import ctypes
    import ctypes.wintypes

    # Get console window handle
    kernel32 = ctypes.windll.kernel32
    user32 = ctypes.windll.user32

    # Hide the console window
    console_window = kernel32.GetConsoleWindow()
    if console_window:
        user32.ShowWindow(console_window, 0)  # SW_HIDE = 0

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from gui.main_window import MainWindow
    from core.config import Config
except ImportError:
    # If running from different directory, try absolute imports
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    from gui.main_window import MainWindow
    from core.config import Config


def setup_application():
    """Setup the QApplication with proper settings"""
    app = QApplication(sys.argv)
    app.setApplicationName("Advanced Mbox Reader")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Andersen Partners")

    # Set application icon
    try:
        # Try to load icon from file first
        icon_path = Path(__file__).parent / "assets" / "icon.ico"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        else:
            # Fallback to embedded icon
            try:
                from assets.icon_data import get_icon_pixmap
                pixmap = get_icon_pixmap()
                if pixmap:
                    app.setWindowIcon(QIcon(pixmap))
            except ImportError:
                pass
    except Exception as e:
        print(f"Could not load application icon: {e}")

    # Enable high DPI scaling (Qt 6.0+ handles this automatically)
    # These attributes are deprecated in Qt 6.0+ and handled automatically
    pass

    return app


def main():
    """Main entry point"""
    app = setup_application()
    
    # Load configuration
    config = Config()
    
    # Create and show main window
    window = MainWindow(config)
    window.show()
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
