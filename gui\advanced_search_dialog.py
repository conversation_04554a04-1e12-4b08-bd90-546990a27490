"""
Advanced search dialog for email filtering
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox,
                               QDateEdit, QGroupBox, QButtonGroup, QRadioButton,
                               QListWidget, QCompleter, QFrame, QSpacerItem,
                               QSizePolicy, QTabWidget, QWidget, QTextEdit)
from PySide6.QtCore import Qt, Signal, QDate, QStringListModel
from PySide6.QtGui import QFont
from datetime import datetime, timedelta
from typing import List

try:
    from ..core.advanced_search import SearchCriteria, SearchType, DateRange
except ImportError:
    from core.advanced_search import SearchCriteria, SearchType, DateRange


class AdvancedSearchDialog(QDialog):
    """Advanced search dialog"""
    
    search_requested = Signal(SearchCriteria)
    
    def __init__(self, search_history: List[str] = None, parent=None):
        super().__init__(parent)
        self.search_history = search_history or []
        self.criteria = SearchCriteria()
        
        self.setWindowTitle("Advanced Search")
        self.setModal(True)
        self.resize(500, 600)
        
        self._setup_ui()
        self._setup_connections()
        self._load_defaults()
    
    def _setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Text search tab
        self._create_text_search_tab()
        
        # Date & Attachments tab
        self._create_filters_tab()
        
        # Advanced tab
        self._create_advanced_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.search_button = QPushButton("Search")
        self.search_button.setDefault(True)
        
        self.clear_button = QPushButton("Clear")
        self.cancel_button = QPushButton("Cancel")
        
        button_layout.addStretch()
        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.search_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def _create_text_search_tab(self):
        """Create text search tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Search query
        query_group = QGroupBox("Search Query")
        query_layout = QVBoxLayout(query_group)
        
        self.query_input = QLineEdit()
        self.query_input.setPlaceholderText("Enter search terms...")
        
        # Add autocomplete
        if self.search_history:
            completer = QCompleter(self.search_history)
            completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.query_input.setCompleter(completer)
        
        query_layout.addWidget(self.query_input)
        layout.addWidget(query_group)
        
        # Search type
        type_group = QGroupBox("Search Type")
        type_layout = QVBoxLayout(type_group)
        
        self.search_type_group = QButtonGroup()
        
        self.simple_radio = QRadioButton("Simple Search")
        self.simple_radio.setChecked(True)
        self.simple_radio.setToolTip("Basic text matching")
        
        self.fuzzy_radio = QRadioButton("Fuzzy Search")
        self.fuzzy_radio.setToolTip("Find similar words (handles typos)")
        
        self.regex_radio = QRadioButton("Regular Expression")
        self.regex_radio.setToolTip("Advanced pattern matching")
        
        self.search_type_group.addButton(self.simple_radio, 0)
        self.search_type_group.addButton(self.fuzzy_radio, 1)
        self.search_type_group.addButton(self.regex_radio, 2)
        
        type_layout.addWidget(self.simple_radio)
        type_layout.addWidget(self.fuzzy_radio)
        type_layout.addWidget(self.regex_radio)
        layout.addWidget(type_group)
        
        # Search fields
        fields_group = QGroupBox("Search In")
        fields_layout = QVBoxLayout(fields_group)
        
        self.subject_check = QCheckBox("Subject")
        self.subject_check.setChecked(True)
        
        self.sender_check = QCheckBox("Sender")
        self.sender_check.setChecked(True)
        
        self.recipients_check = QCheckBox("Recipients (To, CC, BCC)")
        self.recipients_check.setChecked(True)
        
        self.content_check = QCheckBox("Email Content")
        self.content_check.setToolTip("Search in email body text")
        
        fields_layout.addWidget(self.subject_check)
        fields_layout.addWidget(self.sender_check)
        fields_layout.addWidget(self.recipients_check)
        fields_layout.addWidget(self.content_check)
        layout.addWidget(fields_group)
        
        # Options
        options_group = QGroupBox("Options")
        options_layout = QVBoxLayout(options_group)
        
        self.case_sensitive_check = QCheckBox("Case Sensitive")
        options_layout.addWidget(self.case_sensitive_check)
        
        # Fuzzy threshold (only visible for fuzzy search)
        fuzzy_layout = QHBoxLayout()
        self.fuzzy_label = QLabel("Similarity Threshold:")
        self.fuzzy_combo = QComboBox()
        self.fuzzy_combo.addItems(["60%", "70%", "80%", "90%"])
        self.fuzzy_combo.setCurrentText("60%")
        
        fuzzy_layout.addWidget(self.fuzzy_label)
        fuzzy_layout.addWidget(self.fuzzy_combo)
        fuzzy_layout.addStretch()
        
        options_layout.addLayout(fuzzy_layout)
        layout.addWidget(options_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Text Search")
    
    def _create_filters_tab(self):
        """Create date and attachment filters tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Date range
        date_group = QGroupBox("Date Range")
        date_layout = QGridLayout(date_group)
        
        self.date_range_combo = QComboBox()
        self.date_range_combo.addItems([
            "Any Date",
            "Today",
            "Yesterday", 
            "Last Week",
            "Last Month",
            "Last 3 Months",
            "Last 6 Months",
            "Last Year",
            "Custom Range"
        ])
        
        date_layout.addWidget(QLabel("Date Range:"), 0, 0)
        date_layout.addWidget(self.date_range_combo, 0, 1)
        
        # Custom date range
        self.start_date_label = QLabel("From:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setEnabled(False)
        
        self.end_date_label = QLabel("To:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setEnabled(False)
        
        date_layout.addWidget(self.start_date_label, 1, 0)
        date_layout.addWidget(self.start_date_edit, 1, 1)
        date_layout.addWidget(self.end_date_label, 2, 0)
        date_layout.addWidget(self.end_date_edit, 2, 1)
        
        layout.addWidget(date_group)
        
        # Attachments
        attachment_group = QGroupBox("Attachments")
        attachment_layout = QVBoxLayout(attachment_group)
        
        self.attachment_combo = QComboBox()
        self.attachment_combo.addItems([
            "Any (with or without attachments)",
            "Has Attachments",
            "No Attachments"
        ])
        
        attachment_layout.addWidget(QLabel("Attachment Filter:"))
        attachment_layout.addWidget(self.attachment_combo)
        
        # Attachment types
        self.attachment_types_label = QLabel("File Types (comma separated):")
        self.attachment_types_input = QLineEdit()
        self.attachment_types_input.setPlaceholderText("e.g., pdf, jpg, doc, xlsx")
        self.attachment_types_input.setEnabled(False)
        
        attachment_layout.addWidget(self.attachment_types_label)
        attachment_layout.addWidget(self.attachment_types_input)
        
        layout.addWidget(attachment_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Date & Attachments")
    
    def _create_advanced_tab(self):
        """Create advanced options tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Search history
        history_group = QGroupBox("Search History")
        history_layout = QVBoxLayout(history_group)
        
        self.history_list = QListWidget()
        self.history_list.addItems(self.search_history[:10])  # Show last 10
        
        history_layout.addWidget(QLabel("Recent Searches (double-click to use):"))
        history_layout.addWidget(self.history_list)
        
        layout.addWidget(history_group)
        
        # Regex help
        regex_group = QGroupBox("Regular Expression Quick Reference")
        regex_layout = QVBoxLayout(regex_group)
        
        regex_help = QTextEdit()
        regex_help.setMaximumHeight(150)
        regex_help.setReadOnly(True)
        regex_help.setPlainText("""
Common Patterns:
.          - Any character
*          - Zero or more of previous
+          - One or more of previous
?          - Zero or one of previous
^          - Start of line
$          - End of line
[abc]      - Any of a, b, or c
[a-z]      - Any lowercase letter
\\d         - Any digit
\\w         - Any word character
\\s         - Any whitespace
        """.strip())
        
        regex_layout.addWidget(regex_help)
        layout.addWidget(regex_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "Advanced")
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.search_button.clicked.connect(self._on_search)
        self.clear_button.clicked.connect(self._on_clear)
        self.cancel_button.clicked.connect(self.reject)
        
        # Enable/disable custom date fields
        self.date_range_combo.currentTextChanged.connect(self._on_date_range_changed)
        
        # Enable/disable attachment type field
        self.attachment_combo.currentTextChanged.connect(self._on_attachment_filter_changed)
        
        # Show/hide fuzzy options
        self.fuzzy_radio.toggled.connect(self._on_search_type_changed)
        
        # History double-click
        self.history_list.itemDoubleClicked.connect(self._on_history_selected)
    
    def _load_defaults(self):
        """Load default values"""
        self._on_date_range_changed()
        self._on_attachment_filter_changed()
        self._on_search_type_changed()
    
    def _on_search(self):
        """Handle search button click"""
        # Build search criteria
        criteria = SearchCriteria()
        
        # Query and type
        criteria.query = self.query_input.text().strip()
        
        if self.simple_radio.isChecked():
            criteria.search_type = SearchType.SIMPLE
        elif self.fuzzy_radio.isChecked():
            criteria.search_type = SearchType.FUZZY
            # Parse fuzzy threshold
            threshold_text = self.fuzzy_combo.currentText().rstrip('%')
            criteria.fuzzy_threshold = float(threshold_text) / 100.0
        elif self.regex_radio.isChecked():
            criteria.search_type = SearchType.REGEX
        
        # Fields
        fields = []
        if self.subject_check.isChecked():
            fields.append('subject')
        if self.sender_check.isChecked():
            fields.append('sender')
        if self.recipients_check.isChecked():
            fields.append('recipients')
        if self.content_check.isChecked():
            fields.append('content')
        criteria.fields = fields
        
        # Options
        criteria.case_sensitive = self.case_sensitive_check.isChecked()
        
        # Date range
        date_text = self.date_range_combo.currentText()
        if date_text != "Any Date":
            if date_text == "Custom Range":
                criteria.custom_start_date = self.start_date_edit.date().toPython()
                criteria.custom_end_date = self.end_date_edit.date().toPython()
            else:
                date_map = {
                    "Today": DateRange.TODAY,
                    "Yesterday": DateRange.YESTERDAY,
                    "Last Week": DateRange.LAST_WEEK,
                    "Last Month": DateRange.LAST_MONTH,
                    "Last 3 Months": DateRange.LAST_3_MONTHS,
                    "Last 6 Months": DateRange.LAST_6_MONTHS,
                    "Last Year": DateRange.LAST_YEAR
                }
                criteria.date_range = date_map.get(date_text)
        
        # Attachments
        attachment_text = self.attachment_combo.currentText()
        if attachment_text == "Has Attachments":
            criteria.has_attachments = True
        elif attachment_text == "No Attachments":
            criteria.has_attachments = False
        
        # Attachment types
        if self.attachment_types_input.isEnabled() and self.attachment_types_input.text().strip():
            types = [t.strip() for t in self.attachment_types_input.text().split(',')]
            criteria.attachment_types = [t for t in types if t]
        
        self.criteria = criteria
        self.search_requested.emit(criteria)
        self.accept()
    
    def _on_clear(self):
        """Clear all fields"""
        self.query_input.clear()
        self.simple_radio.setChecked(True)
        self.subject_check.setChecked(True)
        self.sender_check.setChecked(True)
        self.recipients_check.setChecked(True)
        self.content_check.setChecked(False)
        self.case_sensitive_check.setChecked(False)
        self.date_range_combo.setCurrentIndex(0)
        self.attachment_combo.setCurrentIndex(0)
        self.attachment_types_input.clear()
    
    def _on_date_range_changed(self):
        """Handle date range combo change"""
        is_custom = self.date_range_combo.currentText() == "Custom Range"
        self.start_date_label.setEnabled(is_custom)
        self.start_date_edit.setEnabled(is_custom)
        self.end_date_label.setEnabled(is_custom)
        self.end_date_edit.setEnabled(is_custom)
    
    def _on_attachment_filter_changed(self):
        """Handle attachment filter change"""
        has_attachments = self.attachment_combo.currentText() == "Has Attachments"
        self.attachment_types_label.setEnabled(has_attachments)
        self.attachment_types_input.setEnabled(has_attachments)
    
    def _on_search_type_changed(self):
        """Handle search type change"""
        is_fuzzy = self.fuzzy_radio.isChecked()
        self.fuzzy_label.setVisible(is_fuzzy)
        self.fuzzy_combo.setVisible(is_fuzzy)
    
    def _on_history_selected(self, item):
        """Handle history item selection"""
        self.query_input.setText(item.text())
        self.tab_widget.setCurrentIndex(0)  # Switch to text search tab
