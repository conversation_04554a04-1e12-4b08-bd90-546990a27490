@echo off
REM Advanced Mbox Reader - GUI Mode (No Console Window)
REM This batch file starts the application without showing a console window

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or newer from https://python.org
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install/update requirements silently
pip install -r requirements.txt >nul 2>&1

REM Start the application in GUI mode (no console window)
start "" pythonw main.pyw
