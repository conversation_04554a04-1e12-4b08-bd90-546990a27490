"""
Configuration management for the mbox reader/editor
"""

import json
import os
from pathlib import Path
from typing import Dict, Any


class Config:
    """Configuration manager with dark mode and performance settings"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".advanced_mailtool"
        self.config_file = self.config_dir / "config.json"
        self.cache_dir = self.config_dir / "cache"
        
        # Default configuration
        self.defaults = {
            "theme": "dark",
            "window_geometry": None,
            "window_state": None,
            "recent_files": [],
            "max_recent_files": 10,
            "cache_enabled": True,
            "cache_size_mb": 500,
            "lazy_loading": True,
            "preview_pane_enabled": True,
            "auto_save_interval": 300,  # seconds
            "email_preview_limit": 1000,  # max emails to show in list
            "attachment_preview_size": 5 * 1024 * 1024,  # 5MB
        }
        
        self._config = self.defaults.copy()
        self._ensure_directories()
        self.load()
    
    def _ensure_directories(self):
        """Ensure config and cache directories exist"""
        self.config_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
    
    def load(self):
        """Load configuration from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self._config.update(saved_config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config: {e}")
    
    def save(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2)
        except IOError as e:
            print(f"Warning: Could not save config: {e}")
    
    def get(self, key: str, default=None) -> Any:
        """Get configuration value"""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """Set configuration value"""
        self._config[key] = value
    
    def add_recent_file(self, filepath: str):
        """Add file to recent files list"""
        recent = self._config.get("recent_files", [])
        if filepath in recent:
            recent.remove(filepath)
        recent.insert(0, filepath)
        
        max_recent = self._config.get("max_recent_files", 10)
        self._config["recent_files"] = recent[:max_recent]
        self.save()
    
    @property
    def cache_directory(self) -> Path:
        """Get cache directory path"""
        return self.cache_dir
