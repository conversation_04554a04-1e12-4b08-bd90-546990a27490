"""
Advanced caching system for email data and performance optimization
"""

import os
import pickle
import hashlib
import time
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from collections import OrderedDict
import sqlite3
import json

from .email_model import EmailModel


class LRUCache:
    """Thread-safe LRU cache implementation"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self.lock:
            if key in self.cache:
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None
    
    def put(self, key: str, value: Any):
        """Put item in cache"""
        with self.lock:
            if key in self.cache:
                # Update existing item
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # Remove least recently used item
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def remove(self, key: str):
        """Remove item from cache"""
        with self.lock:
            self.cache.pop(key, None)
    
    def clear(self):
        """Clear all items from cache"""
        with self.lock:
            self.cache.clear()
    
    def size(self) -> int:
        """Get current cache size"""
        with self.lock:
            return len(self.cache)


class PersistentCache:
    """Persistent cache using SQLite for email metadata and content"""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.cache_dir.mkdir(exist_ok=True)
        self.db_path = cache_dir / "email_cache.db"
        self.lock = threading.RLock()
        
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS email_metadata (
                    cache_key TEXT PRIMARY KEY,
                    mbox_path TEXT,
                    mbox_offset INTEGER,
                    message_id TEXT,
                    subject TEXT,
                    sender TEXT,
                    recipients TEXT,
                    date_str TEXT,
                    has_attachments BOOLEAN,
                    content_loaded BOOLEAN,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS email_content (
                    cache_key TEXT PRIMARY KEY,
                    text_content TEXT,
                    html_content TEXT,
                    raw_content BLOB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cache_key) REFERENCES email_metadata (cache_key)
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_mbox_path_offset 
                ON email_metadata (mbox_path, mbox_offset)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_message_id 
                ON email_metadata (message_id)
            """)
            
            conn.commit()
    
    def get_email_metadata(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get email metadata from cache"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute(
                        "SELECT * FROM email_metadata WHERE cache_key = ?",
                        (cache_key,)
                    )
                    row = cursor.fetchone()
                    
                    if row:
                        # Update access time
                        conn.execute(
                            "UPDATE email_metadata SET accessed_at = CURRENT_TIMESTAMP WHERE cache_key = ?",
                            (cache_key,)
                        )
                        conn.commit()
                        
                        return dict(row)
                    
                    return None
            except Exception as e:
                print(f"Error getting email metadata: {e}")
                return None
    
    def put_email_metadata(self, cache_key: str, email: EmailModel):
        """Store email metadata in cache"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO email_metadata 
                        (cache_key, mbox_path, mbox_offset, message_id, subject, sender, 
                         recipients, date_str, has_attachments, content_loaded)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        cache_key,
                        email.mbox_path,
                        email.mbox_offset,
                        email.message_id,
                        email.subject,
                        email.sender,
                        email.recipients,
                        email.date_str,
                        email.has_attachments,
                        email._content_loaded
                    ))
                    conn.commit()
            except Exception as e:
                print(f"Error storing email metadata: {e}")
    
    def get_email_content(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get email content from cache"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute(
                        "SELECT * FROM email_content WHERE cache_key = ?",
                        (cache_key,)
                    )
                    row = cursor.fetchone()
                    
                    if row:
                        return dict(row)
                    
                    return None
            except Exception as e:
                print(f"Error getting email content: {e}")
                return None
    
    def put_email_content(self, cache_key: str, text_content: str, 
                         html_content: str, raw_content: bytes = None):
        """Store email content in cache"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT OR REPLACE INTO email_content 
                        (cache_key, text_content, html_content, raw_content)
                        VALUES (?, ?, ?, ?)
                    """, (cache_key, text_content, html_content, raw_content))
                    conn.commit()
            except Exception as e:
                print(f"Error storing email content: {e}")
    
    def cleanup_old_entries(self, days: int = 30):
        """Remove cache entries older than specified days"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        DELETE FROM email_content 
                        WHERE cache_key IN (
                            SELECT cache_key FROM email_metadata 
                            WHERE accessed_at < datetime('now', '-{} days')
                        )
                    """.format(days))
                    
                    conn.execute("""
                        DELETE FROM email_metadata 
                        WHERE accessed_at < datetime('now', '-{} days')
                    """.format(days))
                    
                    conn.commit()
            except Exception as e:
                print(f"Error cleaning up cache: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("SELECT COUNT(*) FROM email_metadata")
                    metadata_count = cursor.fetchone()[0]
                    
                    cursor = conn.execute("SELECT COUNT(*) FROM email_content")
                    content_count = cursor.fetchone()[0]
                    
                    # Get database file size
                    db_size = self.db_path.stat().st_size if self.db_path.exists() else 0
                    
                    return {
                        'metadata_entries': metadata_count,
                        'content_entries': content_count,
                        'database_size_mb': db_size / (1024 * 1024),
                        'database_path': str(self.db_path)
                    }
            except Exception as e:
                print(f"Error getting cache stats: {e}")
                return {}
    
    def clear_cache(self):
        """Clear all cache entries"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("DELETE FROM email_content")
                    conn.execute("DELETE FROM email_metadata")
                    conn.commit()
            except Exception as e:
                print(f"Error clearing cache: {e}")


class CacheManager:
    """Main cache manager coordinating memory and persistent caches"""
    
    def __init__(self, cache_dir: Path, memory_cache_size: int = 500):
        self.cache_dir = cache_dir
        self.memory_cache = LRUCache(memory_cache_size)
        self.persistent_cache = PersistentCache(cache_dir)
        self.lock = threading.RLock()
        
        # Performance metrics
        self.stats = {
            'memory_hits': 0,
            'persistent_hits': 0,
            'misses': 0,
            'stores': 0
        }
    
    def get_email(self, cache_key: str) -> Optional[EmailModel]:
        """Get email from cache (memory first, then persistent)"""
        with self.lock:
            # Try memory cache first
            email = self.memory_cache.get(cache_key)
            if email:
                self.stats['memory_hits'] += 1
                return email
            
            # Try persistent cache
            metadata = self.persistent_cache.get_email_metadata(cache_key)
            if metadata:
                self.stats['persistent_hits'] += 1
                
                # Reconstruct EmailModel from cached metadata
                # Note: This is a simplified reconstruction
                # In a real implementation, you'd need to properly reconstruct the email
                return None  # Placeholder - would need full implementation
            
            self.stats['misses'] += 1
            return None
    
    def put_email(self, cache_key: str, email: EmailModel):
        """Store email in both memory and persistent cache"""
        with self.lock:
            # Store in memory cache
            self.memory_cache.put(cache_key, email)
            
            # Store metadata in persistent cache
            self.persistent_cache.put_email_metadata(cache_key, email)
            
            # Store content if loaded
            if email._content_loaded:
                self.persistent_cache.put_email_content(
                    cache_key,
                    email.text_content,
                    email.html_content
                )
            
            self.stats['stores'] += 1
    
    def invalidate_email(self, cache_key: str):
        """Remove email from all caches"""
        with self.lock:
            self.memory_cache.remove(cache_key)
            # Note: Persistent cache cleanup would be implemented here
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        with self.lock:
            persistent_stats = self.persistent_cache.get_cache_stats()
            
            total_requests = (self.stats['memory_hits'] + 
                            self.stats['persistent_hits'] + 
                            self.stats['misses'])
            
            hit_rate = 0
            if total_requests > 0:
                hit_rate = ((self.stats['memory_hits'] + self.stats['persistent_hits']) / 
                           total_requests) * 100
            
            return {
                'memory_cache_size': self.memory_cache.size(),
                'memory_hits': self.stats['memory_hits'],
                'persistent_hits': self.stats['persistent_hits'],
                'cache_misses': self.stats['misses'],
                'total_stores': self.stats['stores'],
                'hit_rate_percent': round(hit_rate, 2),
                'persistent_cache': persistent_stats
            }
    
    def cleanup(self, days: int = 30):
        """Cleanup old cache entries"""
        with self.lock:
            self.persistent_cache.cleanup_old_entries(days)
    
    def clear_all_caches(self):
        """Clear all caches"""
        with self.lock:
            self.memory_cache.clear()
            self.persistent_cache.clear_cache()
            
            # Reset stats
            self.stats = {
                'memory_hits': 0,
                'persistent_hits': 0,
                'misses': 0,
                'stores': 0
            }
