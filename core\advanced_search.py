"""
Advanced search functionality for email filtering and searching
"""

import re
import difflib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .email_model import EmailModel


class SearchType(Enum):
    """Types of search operations"""
    SIMPLE = "simple"
    FUZZY = "fuzzy"
    REGEX = "regex"
    DATE_RANGE = "date_range"
    ATTACHMENT = "attachment"
    COMBINED = "combined"


class DateRange(Enum):
    """Predefined date ranges"""
    TODAY = "today"
    YESTERDAY = "yesterday"
    LAST_WEEK = "last_week"
    LAST_MONTH = "last_month"
    LAST_3_MONTHS = "last_3_months"
    LAST_6_MONTHS = "last_6_months"
    LAST_YEAR = "last_year"
    CUSTOM = "custom"


@dataclass
class SearchCriteria:
    """Search criteria container"""
    query: str = ""
    search_type: SearchType = SearchType.SIMPLE
    fields: List[str] = None  # ['subject', 'sender', 'recipients', 'content']
    date_range: DateRange = None
    custom_start_date: Optional[datetime] = None
    custom_end_date: Optional[datetime] = None
    has_attachments: Optional[bool] = None
    attachment_types: List[str] = None  # ['.pdf', '.jpg', '.doc']
    case_sensitive: bool = False
    fuzzy_threshold: float = 0.6  # For fuzzy matching
    
    def __post_init__(self):
        if self.fields is None:
            self.fields = ['subject', 'sender', 'recipients']


class AdvancedSearchEngine:
    """Advanced search engine for emails"""

    def __init__(self):
        self.search_history: List[SearchCriteria] = []
        self.max_history = 50

        # Performance optimization
        self.search_cache: Dict[str, List[EmailModel]] = {}
        self.max_cache_size = 20
    
    def search(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Perform advanced search on emails with caching"""
        if not emails:
            return []

        # Create cache key for simple text searches
        cache_key = None
        if (criteria.search_type == SearchType.SIMPLE and
            criteria.query.strip() and
            not criteria.date_range and
            not criteria.custom_start_date and
            not criteria.custom_end_date and
            criteria.has_attachments is None and
            not criteria.attachment_types):

            cache_key = f"{criteria.query.lower()}_{','.join(sorted(criteria.fields))}_{criteria.case_sensitive}"

            # Check cache
            if cache_key in self.search_cache:
                # Verify cache is still valid (same email list)
                cached_results = self.search_cache[cache_key]
                if len(cached_results) <= len(emails):  # Simple validation
                    return cached_results

        # Add to search history
        self._add_to_history(criteria)

        # Apply filters in sequence
        results = emails.copy()

        # Date range filter
        if criteria.date_range or criteria.custom_start_date or criteria.custom_end_date:
            results = self._filter_by_date(results, criteria)

        # Attachment filter
        if criteria.has_attachments is not None or criteria.attachment_types:
            results = self._filter_by_attachments(results, criteria)

        # Text search
        if criteria.query.strip():
            results = self._filter_by_text(results, criteria)

        # Cache simple search results
        if cache_key:
            self._add_to_cache(cache_key, results)

        return results
    
    def _filter_by_date(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Filter emails by date range"""
        start_date, end_date = self._get_date_range(criteria)
        
        if not start_date and not end_date:
            return emails
        
        filtered = []
        for email in emails:
            if not email.date:
                continue
            
            email_date = email.date.replace(tzinfo=None) if email.date.tzinfo else email.date
            
            if start_date and email_date < start_date:
                continue
            if end_date and email_date > end_date:
                continue
            
            filtered.append(email)
        
        return filtered
    
    def _filter_by_attachments(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Filter emails by attachment criteria"""
        filtered = []
        
        for email in emails:
            # Check if email has attachments
            if criteria.has_attachments is not None:
                has_attachments = email.has_attachments
                if criteria.has_attachments != has_attachments:
                    continue
            
            # Check attachment types
            if criteria.attachment_types:
                if not email.has_attachments:
                    continue
                
                attachments = email.attachments
                attachment_extensions = [att.filename.lower().split('.')[-1] 
                                       for att in attachments if '.' in att.filename]
                
                # Check if any attachment matches the required types
                required_types = [t.lower().lstrip('.') for t in criteria.attachment_types]
                if not any(ext in required_types for ext in attachment_extensions):
                    continue
            
            filtered.append(email)
        
        return filtered
    
    def _filter_by_text(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Filter emails by text search"""
        if criteria.search_type == SearchType.SIMPLE:
            return self._simple_search(emails, criteria)
        elif criteria.search_type == SearchType.FUZZY:
            return self._fuzzy_search(emails, criteria)
        elif criteria.search_type == SearchType.REGEX:
            return self._regex_search(emails, criteria)
        else:
            return self._simple_search(emails, criteria)
    
    def _simple_search(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Simple text search - highly optimized for performance"""
        query = criteria.query if criteria.case_sensitive else criteria.query.lower()

        # Early return for empty query
        if not query.strip():
            return emails

        # Pre-compile search terms for better performance
        search_terms = query.split() if ' ' in query else [query]
        filtered = []

        # Pre-compile field getters for performance
        field_getters = {
            'subject': lambda e: e.subject,
            'sender': lambda e: e.sender,
            'recipients': lambda e: f"{e.recipients} {e.cc} {e.bcc}",
            'content': lambda e: f"{e.text_content} {e.html_content}"
        }

        # Use set for faster field lookup
        fields_set = set(criteria.fields)

        for email in emails:
            # Quick check: if any search term is in any field, include email
            found_match = False

            for field in fields_set:
                if field in field_getters:
                    try:
                        field_value = field_getters[field](email)
                        if field_value:
                            search_value = field_value if criteria.case_sensitive else field_value.lower()

                            # Fast check: if any term is found, it's a match
                            if any(term in search_value for term in search_terms):
                                found_match = True
                                break
                    except (AttributeError, TypeError):
                        # Skip if field access fails
                        continue

            if found_match:
                filtered.append(email)

        return filtered
    
    def _fuzzy_search(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Fuzzy text search using difflib"""
        filtered = []
        
        for email in emails:
            match = False
            
            for field in criteria.fields:
                field_value = self._get_field_value(email, field)
                if not field_value:
                    continue
                
                # Split into words for better fuzzy matching
                words = field_value.lower().split()
                query_words = criteria.query.lower().split()
                
                for query_word in query_words:
                    for word in words:
                        similarity = difflib.SequenceMatcher(None, query_word, word).ratio()
                        if similarity >= criteria.fuzzy_threshold:
                            match = True
                            break
                    if match:
                        break
                if match:
                    break
            
            if match:
                filtered.append(email)
        
        return filtered
    
    def _regex_search(self, emails: List[EmailModel], criteria: SearchCriteria) -> List[EmailModel]:
        """Regex text search"""
        try:
            flags = 0 if criteria.case_sensitive else re.IGNORECASE
            pattern = re.compile(criteria.query, flags)
        except re.error:
            # Invalid regex, fall back to simple search
            return self._simple_search(emails, criteria)
        
        filtered = []
        
        for email in emails:
            match = False
            
            for field in criteria.fields:
                field_value = self._get_field_value(email, field)
                if not field_value:
                    continue
                
                if pattern.search(field_value):
                    match = True
                    break
            
            if match:
                filtered.append(email)
        
        return filtered
    
    def _get_field_value(self, email: EmailModel, field: str) -> str:
        """Get field value from email"""
        if field == 'subject':
            return email.subject
        elif field == 'sender':
            return email.sender
        elif field == 'recipients':
            return f"{email.recipients} {email.cc} {email.bcc}"
        elif field == 'content':
            return f"{email.text_content} {email.html_content}"
        else:
            return getattr(email, field, "")
    
    def _get_date_range(self, criteria: SearchCriteria) -> tuple:
        """Get start and end dates from criteria"""
        now = datetime.now()
        start_date = None
        end_date = None
        
        if criteria.custom_start_date:
            start_date = criteria.custom_start_date
        if criteria.custom_end_date:
            end_date = criteria.custom_end_date
        
        if criteria.date_range:
            if criteria.date_range == DateRange.TODAY:
                start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
            elif criteria.date_range == DateRange.YESTERDAY:
                yesterday = now - timedelta(days=1)
                start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
            elif criteria.date_range == DateRange.LAST_WEEK:
                start_date = now - timedelta(days=7)
            elif criteria.date_range == DateRange.LAST_MONTH:
                start_date = now - timedelta(days=30)
            elif criteria.date_range == DateRange.LAST_3_MONTHS:
                start_date = now - timedelta(days=90)
            elif criteria.date_range == DateRange.LAST_6_MONTHS:
                start_date = now - timedelta(days=180)
            elif criteria.date_range == DateRange.LAST_YEAR:
                start_date = now - timedelta(days=365)
        
        return start_date, end_date
    
    def _add_to_history(self, criteria: SearchCriteria):
        """Add search criteria to history"""
        # Remove duplicates
        self.search_history = [c for c in self.search_history 
                              if c.query != criteria.query or c.search_type != criteria.search_type]
        
        # Add to beginning
        self.search_history.insert(0, criteria)
        
        # Limit history size
        if len(self.search_history) > self.max_history:
            self.search_history = self.search_history[:self.max_history]
    
    def get_search_suggestions(self, partial_query: str, limit: int = 5) -> List[str]:
        """Get search suggestions based on history"""
        if not partial_query:
            return [c.query for c in self.search_history[:limit] if c.query]
        
        suggestions = []
        partial_lower = partial_query.lower()
        
        for criteria in self.search_history:
            if criteria.query and partial_lower in criteria.query.lower():
                if criteria.query not in suggestions:
                    suggestions.append(criteria.query)
                if len(suggestions) >= limit:
                    break
        
        return suggestions

    def _add_to_cache(self, cache_key: str, results: List[EmailModel]):
        """Add search results to cache"""
        # Limit cache size
        if len(self.search_cache) >= self.max_cache_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.search_cache))
            del self.search_cache[oldest_key]

        self.search_cache[cache_key] = results

    def clear_cache(self):
        """Clear search cache"""
        self.search_cache.clear()
