# Advanced Mbox Reader/Editor

En moderne, højtydende mbox fil læser og editor bygget med PySide6 og Python.

## Features

### 🎨 Moderne GUI
- **Dark Mode**: Elegant mørkt tema som standard
- **Responsiv Interface**: Flydende og hurtig brugeroplevelse
- **Intuitivt Design**: Nem at navigere og bruge

### 📧 Email Funktionalitet
- **Mbox Support**: Læs og skriv mbox filer effektivt
- **Rich Email Viewer**: Vis HTML emails, billeder og attachments korrekt
- **Email Liste**: Hurtig oversigt over alle emails med søgning
- **Attachment Håndtering**: Vis, gem og eksporter vedhæftninger

### ⚡ Performance
- **Lazy Loading**: Indlæs kun det nødvendige for hurtig start
- **Memory Mapping**: Effektiv håndtering af store mbox filer
- **Caching System**: Intelligent caching for bedre performance
- **Threading**: Background operationer holder UI responsiv

### 📤 Import/Export
- **Multiple Formater**: EML, MBOX, HTML, TXT, JSON
- **Batch Export**: Eksporter flere emails på én gang
- **Archive Support**: Opret komprimerede arkiver
- **Attachment Export**: Gem alle vedhæftninger

## Installation

### Krav
- Python 3.8 eller nyere
- Windows 10/11 (kan tilpasses til Linux/macOS)

### Setup
1. Klon eller download projektet
2. Installer dependencies:
```bash
pip install -r requirements.txt
```

3. Kør applikationen:
```bash
python main.py
```

## Brug

### Åbn Mbox Fil
1. Klik på "File" → "Open Mbox File..." eller tryk Ctrl+O
2. Vælg din mbox fil
3. Vent på at emails bliver indlæst

### Læs Emails
- Klik på en email i listen til venstre for at se indholdet
- Brug tabs til at skifte mellem Rich View, Plain Text og Raw Source
- Dobbeltklik på attachments for at åbne dem

### Søg i Emails
- Brug søgefeltet øverst til at finde emails
- Søg i emne, afsender eller modtager

### Eksporter Emails
1. Vælg email(s) eller lad alle være valgt
2. Klik på "Tools" → "Export Emails..."
3. Vælg format og indstillinger
4. Klik "Export"

## Arkitektur

### Core Komponenter
- **MboxParser**: Effektiv parsing af mbox filer med memory mapping
- **EmailModel**: Data model med lazy loading af content
- **AttachmentManager**: Håndtering og caching af attachments
- **CacheManager**: Intelligent caching system
- **PerformanceMonitor**: Performance overvågning og optimering

### GUI Komponenter
- **MainWindow**: Hovedvindue med menu, toolbar og status
- **EmailListWidget**: Virtualiseret email liste
- **EmailViewerWidget**: Rich content viewer med tabs
- **ExportDialog**: Avanceret export dialog

## Performance Tips

### For Store Mbox Filer
- Applikationen bruger memory mapping for effektiv håndtering
- Lazy loading betyder hurtig start selv med tusindvis af emails
- Cache systemet forbedrer performance ved gentagende brug

### Memory Brug
- Caching kan justeres i indstillingerne
- Ryd cache regelmæssigt for at frigøre plads
- Performance monitor viser aktuel resource brug

## Fejlfinding

### Almindelige Problemer

**Mbox fil kan ikke åbnes**
- Kontroller at filen er en gyldig mbox fil
- Sørg for at du har læserettigheder til filen

**Langsom performance**
- Kontroller tilgængelig RAM
- Ryd cache hvis den er blevet for stor
- Luk andre tunge applikationer

**Attachments vises ikke**
- Nogle email formater understøttes ikke fuldt ud
- Prøv at eksportere emailen og åbn i anden email klient

### Log Filer
Applikationen logger fejl til konsollen. Kør fra kommandolinjen for at se detaljerede fejlmeddelelser.

## Udvikling

### Projekt Struktur
```
advanced_mailtool/
├── main.py                 # Entry point
├── core/                   # Core funktionalitet
│   ├── config.py          # Konfiguration
│   ├── mbox_parser.py     # Mbox parsing
│   ├── email_model.py     # Email data model
│   ├── attachment_manager.py # Attachment håndtering
│   ├── import_export.py   # Import/export
│   ├── cache_manager.py   # Caching system
│   └── performance_monitor.py # Performance
├── gui/                    # GUI komponenter
│   ├── main_window.py     # Hovedvindue
│   ├── email_list_widget.py # Email liste
│   ├── email_viewer_widget.py # Email viewer
│   ├── export_dialog.py   # Export dialog
│   └── styles.py          # Dark mode styles
└── requirements.txt        # Dependencies
```

### Tilføj Features
1. Implementer ny funktionalitet i `core/` modulet
2. Tilføj GUI komponenter i `gui/` modulet
3. Opdater main window for integration
4. Test grundigt

## Licens

Dette projekt er udviklet til Andersen Partners og er til intern brug.

## Support

For spørgsmål eller problemer, kontakt udviklingsteamet.
