"""
Main window for the mbox reader/editor application
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout,
                               QSplitter, QMenuBar, QMenu, QToolBar, QStatusBar,
                               QFileDialog, QMessageBox, QProgressBar, QLabel,
                               QLineEdit, QPushButton, QFrame, QButtonGroup,
                               QTabWidget)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, QSize
from PySide6.QtGui import QAction, QIcon, QKeySequence

try:
    from .styles import DARK_STYLE, HIGH_CONTRAST_SCROLLBAR
    from .email_list_widget import EmailListWidget
    from .email_viewer_widget import EmailViewerWidget
    from .export_dialog import ExportDialog
    from .advanced_search_dialog import AdvancedSearchDialog
    from .attachment_manager_dialog import AttachmentManagerDialog
    from ..core.config import Config
    from ..core.mbox_parser import MboxPars<PERSON>
    from ..core.email_model import EmailModel
    from ..core.import_export import EmailExporter, EmailImporter
    from ..core.advanced_search import AdvancedSearchEngine, SearchCriteria
except ImportError:
    # Fallback for direct execution
    from gui.styles import DARK_STYLE, HIGH_CONTRAST_SCROLLBAR
    from gui.email_list_widget import EmailListWidget
    from gui.email_viewer_widget import EmailViewerWidget
    from gui.export_dialog import ExportDialog
    from gui.advanced_search_dialog import AdvancedSearchDialog
    from gui.attachment_manager_dialog import AttachmentManagerDialog
    from core.config import Config
    from core.mbox_parser import MboxParser
    from core.email_model import EmailModel
    from core.import_export import EmailExporter, EmailImporter
    from core.advanced_search import AdvancedSearchEngine, SearchCriteria


class AsyncSearchThread(QThread):
    """Asynchronous search thread to prevent UI blocking"""

    search_completed = Signal(list, str)  # results, query
    search_progress = Signal(str)  # status message

    def __init__(self, search_engine, emails, query):
        super().__init__()
        self.search_engine = search_engine
        self.emails = emails
        self.query = query
        self.should_stop = False

    def run(self):
        """Perform search in background thread"""
        try:
            if self.should_stop:
                return

            self.search_progress.emit("Searching...")

            if self.query.strip():
                criteria = SearchCriteria(query=self.query)
                results = self.search_engine.search(self.emails, criteria)
            else:
                results = self.emails

            if not self.should_stop:
                self.search_completed.emit(results, self.query)

        except Exception as e:
            print(f"Search error: {e}")
            if not self.should_stop:
                self.search_completed.emit(self.emails, self.query)

    def stop(self):
        """Stop the search thread"""
        self.should_stop = True


class MboxLoadThread(QThread):
    """Thread for loading mbox files in background"""

    progress_updated = Signal(int, str)
    emails_loaded = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, filepath: str, cache_dir: Path = None):
        super().__init__()
        self.filepath = filepath
        self.cache_dir = cache_dir
        self._parser = None
    
    def run(self):
        """Load mbox file in background"""
        try:
            self.progress_updated.emit(10, "Opening mbox file...")
            
            self._parser = MboxParser(self.filepath, self.cache_dir)
            self._parser.open()
            
            self.progress_updated.emit(30, "Parsing email headers...")
            
            emails = self._parser.parse_headers_only()
            
            self.progress_updated.emit(100, f"Loaded {len(emails)} emails")
            self.emails_loaded.emit(emails)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def get_parser(self) -> MboxParser:
        """Get the mbox parser instance"""
        return self._parser


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        # Multi-tab support
        self.tab_widget = None  # Will be created in _setup_ui
        self.tabs_data = {}  # Tab ID -> {emails, parser, search_engine, filtered_emails}
        self.current_tab_id = None

        # Legacy properties for backward compatibility
        self.current_parser = None
        self.current_emails = []
        self.load_thread = None
        self.search_engine = AdvancedSearchEngine()
        self.filtered_emails = []

        # Search optimization
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._perform_search)
        self.search_delay = self.config.get("search_delay", 300)  # milliseconds
        self.last_search_query = ""

        # Async search
        self.search_thread = None
        self.search_cancelled = False

        # Fast clear optimization
        self.is_showing_all_emails = True

        self._setup_ui()
        self._setup_connections()
        self._apply_theme()
        self._set_window_icon()
        self._load_scrollbar_preference()
        self._load_search_preferences()
        self._update_ui_state()
        self._restore_geometry()
    
    def _setup_ui(self):
        """Setup the main window UI"""
        self.setWindowTitle("Advanced Mbox Reader")
        self.setMinimumSize(1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create toolbar
        self._create_toolbar()
        
        # Create search bar
        search_frame = self._create_search_bar()
        main_layout.addWidget(search_frame)
        
        # Create main content area
        content_splitter = self._create_content_area()
        main_layout.addWidget(content_splitter)
        
        # Create menu bar
        self._create_menu_bar()
        
        # Create status bar
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        open_action = QAction("&Open Mbox File...", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self._open_mbox_file)
        file_menu.addAction(open_action)

        open_new_tab_action = QAction("Open in &New Tab...", self)
        open_new_tab_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
        open_new_tab_action.triggered.connect(self._open_mbox_in_new_tab)
        file_menu.addAction(open_new_tab_action)

        file_menu.addSeparator()

        close_action = QAction("&Close Mbox", self)
        close_action.setShortcut(QKeySequence.Close)
        close_action.triggered.connect(self._close_mbox)
        file_menu.addAction(close_action)

        file_menu.addSeparator()
        
        # Recent files submenu
        self.recent_menu = file_menu.addMenu("Recent Files")
        self._update_recent_files_menu()
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")

        refresh_action = QAction("&Refresh", self)
        refresh_action.setShortcut(QKeySequence.Refresh)
        refresh_action.triggered.connect(self._refresh_current_mbox)
        view_menu.addAction(refresh_action)

        view_menu.addSeparator()

        # Scrollbar style options
        scrollbar_menu = view_menu.addMenu("Scrollbar Style")

        self.normal_scrollbar_action = QAction("Normal", self)
        self.normal_scrollbar_action.setCheckable(True)
        self.normal_scrollbar_action.setChecked(True)
        self.normal_scrollbar_action.triggered.connect(lambda: self._set_scrollbar_style("normal"))
        scrollbar_menu.addAction(self.normal_scrollbar_action)

        self.high_contrast_scrollbar_action = QAction("High Contrast", self)
        self.high_contrast_scrollbar_action.setCheckable(True)
        self.high_contrast_scrollbar_action.triggered.connect(lambda: self._set_scrollbar_style("high_contrast"))
        scrollbar_menu.addAction(self.high_contrast_scrollbar_action)

        view_menu.addSeparator()

        # Search performance options
        search_menu = view_menu.addMenu("Search Performance")

        fast_search_action = QAction("Fast (100ms delay)", self)
        fast_search_action.setCheckable(True)
        fast_search_action.triggered.connect(lambda: self._set_search_delay(100))
        search_menu.addAction(fast_search_action)

        normal_search_action = QAction("Normal (300ms delay)", self)
        normal_search_action.setCheckable(True)
        normal_search_action.setChecked(True)
        normal_search_action.triggered.connect(lambda: self._set_search_delay(300))
        search_menu.addAction(normal_search_action)

        slow_search_action = QAction("Slow (500ms delay)", self)
        slow_search_action.setCheckable(True)
        slow_search_action.triggered.connect(lambda: self._set_search_delay(500))
        search_menu.addAction(slow_search_action)

        # Store search actions for later reference
        self.search_delay_actions = {
            100: fast_search_action,
            300: normal_search_action,
            500: slow_search_action
        }
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        # Search actions
        find_action = QAction("&Find...", self)
        find_action.setShortcut(QKeySequence.Find)
        find_action.triggered.connect(self._focus_search)
        tools_menu.addAction(find_action)

        advanced_search_action = QAction("&Advanced Search...", self)
        advanced_search_action.setShortcut(QKeySequence("Ctrl+Shift+F"))
        advanced_search_action.triggered.connect(self._show_advanced_search)
        tools_menu.addAction(advanced_search_action)

        clear_search_action = QAction("&Clear Search", self)
        clear_search_action.setShortcut(QKeySequence("Escape"))
        clear_search_action.triggered.connect(self._clear_search)
        tools_menu.addAction(clear_search_action)

        tools_menu.addSeparator()

        # Attachment actions
        attachment_manager_action = QAction("&Attachment Manager...", self)
        attachment_manager_action.setShortcut(QKeySequence("Ctrl+Alt+A"))
        attachment_manager_action.triggered.connect(self._show_attachment_manager)
        tools_menu.addAction(attachment_manager_action)

        tools_menu.addSeparator()

        export_action = QAction("&Export Emails...", self)
        export_action.triggered.connect(self._export_emails)
        tools_menu.addAction(export_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        about_action = QAction("&About", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_toolbar(self):
        """Create the toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setMovable(False)
        
        # Open file action
        open_action = QAction("Open", self)
        open_action.setToolTip("Open mbox file")
        open_action.triggered.connect(self._open_mbox_file)
        toolbar.addAction(open_action)

        # Close file action
        self.close_action = QAction("Close", self)
        self.close_action.setToolTip("Close current mbox file")
        self.close_action.triggered.connect(self._close_mbox)
        self.close_action.setEnabled(False)  # Disabled until mbox is loaded
        toolbar.addAction(self.close_action)

        toolbar.addSeparator()
        
        # Refresh action
        refresh_action = QAction("Refresh", self)
        refresh_action.setToolTip("Refresh current mbox file")
        refresh_action.triggered.connect(self._refresh_current_mbox)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # Export action
        export_action = QAction("Export", self)
        export_action.setToolTip("Export emails")
        export_action.triggered.connect(self._export_emails)
        toolbar.addAction(export_action)
    
    def _create_search_bar(self) -> QFrame:
        """Create the search bar"""
        search_frame = QFrame()
        search_frame.setFrameStyle(QFrame.StyledPanel)
        search_frame.setMaximumHeight(50)
        
        layout = QHBoxLayout(search_frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Search label
        search_label = QLabel("Search:")
        layout.addWidget(search_label)
        
        # Search input with clear button
        search_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search emails by subject, sender, or recipient...")
        self.search_input.textChanged.connect(self._on_search_text_changed)
        self.search_input.setEnabled(False)  # Disabled until mbox is loaded

        self.clear_search_button = QPushButton("Clear")
        self.clear_search_button.clicked.connect(self._clear_search)
        self.clear_search_button.setEnabled(False)  # Disabled until mbox is loaded
        self.clear_search_button.setMaximumWidth(60)

        # Export search results button (hidden by default)
        self.export_search_button = QPushButton("Export Search Results")
        self.export_search_button.clicked.connect(self._export_search_results)
        self.export_search_button.setVisible(False)

        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.clear_search_button)
        search_layout.addWidget(self.export_search_button)

        layout.addLayout(search_layout)

        return search_frame
    
    def _create_content_area(self) -> QSplitter:
        """Create the main content area with email list and viewer"""
        splitter = QSplitter(Qt.Horizontal)
        
        # Email list
        self.email_list = EmailListWidget()
        self.email_list.setMaximumWidth(400)
        splitter.addWidget(self.email_list)
        
        # Email viewer
        self.email_viewer = EmailViewerWidget()
        self.email_viewer.setEnabled(False)  # Disabled until mbox is loaded
        splitter.addWidget(self.email_viewer)
        
        # Set initial sizes
        splitter.setSizes([300, 700])
        
        return splitter
    
    def _create_status_bar(self):
        """Create the status bar"""
        self.status_bar = self.statusBar()
        
        # Email count label
        self.email_count_label = QLabel("No emails loaded")
        self.status_bar.addWidget(self.email_count_label)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_bar.addPermanentWidget(self.status_label)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.email_list.email_selected.connect(self._on_email_selected)
        self.email_list.email_double_clicked.connect(self._on_email_double_clicked)
    
    def _apply_theme(self):
        """Apply dark theme to the application"""
        self.setStyleSheet(DARK_STYLE)

    def _set_scrollbar_style(self, style: str):
        """Set scrollbar style"""
        # Update menu checkmarks
        if style == "high_contrast":
            self.normal_scrollbar_action.setChecked(False)
            self.high_contrast_scrollbar_action.setChecked(True)

            # Apply high contrast scrollbar style on top of dark theme
            current_style = self.styleSheet()
            # Remove existing scrollbar styles and add high contrast ones
            lines = current_style.split('\n')
            filtered_lines = []
            skip_scrollbar = False

            for line in lines:
                if 'QScrollBar' in line and '{' in line:
                    skip_scrollbar = True
                elif skip_scrollbar and '}' in line and not '{' in line:
                    skip_scrollbar = False
                    continue
                elif not skip_scrollbar:
                    filtered_lines.append(line)

            new_style = '\n'.join(filtered_lines) + '\n' + HIGH_CONTRAST_SCROLLBAR
            self.setStyleSheet(new_style)

            # Save preference
            self.config.set("scrollbar_style", "high_contrast")
        else:
            self.normal_scrollbar_action.setChecked(True)
            self.high_contrast_scrollbar_action.setChecked(False)

            # Apply normal dark theme
            self.setStyleSheet(DARK_STYLE)

            # Save preference
            self.config.set("scrollbar_style", "normal")

        self.config.save()

    def _set_search_delay(self, delay_ms: int):
        """Set search delay in milliseconds"""
        self.search_delay = delay_ms

        # Update menu checkmarks
        for delay, action in self.search_delay_actions.items():
            action.setChecked(delay == delay_ms)

        # Save preference
        self.config.set("search_delay", delay_ms)
        self.config.save()

        # Update status
        self.status_label.setText(f"Search delay set to {delay_ms}ms")
        QTimer.singleShot(2000, lambda: self.status_label.setText("Ready"))

    def _load_scrollbar_preference(self):
        """Load saved scrollbar style preference"""
        scrollbar_style = self.config.get("scrollbar_style", "normal")

        if scrollbar_style == "high_contrast":
            # Apply style (this will also update menu selection)
            self._set_scrollbar_style("high_contrast")
        else:
            # Default to normal (already set in menu creation)
            pass

    def _load_search_preferences(self):
        """Load saved search preferences"""
        # Load search delay preference
        saved_delay = self.config.get("search_delay", 300)
        if saved_delay in self.search_delay_actions:
            self.search_delay = saved_delay
            self.search_delay_actions[saved_delay].setChecked(True)
            # Uncheck others
            for delay, action in self.search_delay_actions.items():
                if delay != saved_delay:
                    action.setChecked(False)

    def _update_ui_state(self):
        """Update UI state based on whether mbox is loaded"""
        has_emails = bool(self.current_emails)

        # Enable/disable search controls
        self.search_input.setEnabled(has_emails)
        self.clear_search_button.setEnabled(has_emails)

        # Enable/disable close action
        if hasattr(self, 'close_action'):
            self.close_action.setEnabled(has_emails)

        # Email viewer should be enabled if mbox is loaded, but tabs disabled until email selected
        if has_emails:
            self.email_viewer.setEnabled(True)
            # Disable tabs until an email is selected (unless one is already selected)
            current_selection = self.email_list.currentItem()
            self._update_email_viewer_tabs(current_selection is not None)
        else:
            self.email_viewer.setEnabled(False)
            self._update_email_viewer_tabs(False)

        # Update search input placeholder
        if has_emails:
            self.search_input.setPlaceholderText("Search emails by subject, sender, or recipient...")
        else:
            self.search_input.setPlaceholderText("Open an mbox file to enable search...")

    def _update_email_viewer_tabs(self, email_selected: bool):
        """Enable/disable email viewer tabs based on email selection"""
        if hasattr(self.email_viewer, 'set_tabs_enabled'):
            self.email_viewer.set_tabs_enabled(email_selected)

    def _update_export_search_button(self):
        """Show/hide export search results button based on search state"""
        has_search_results = False

        if (hasattr(self, 'filtered_emails') and
            self.filtered_emails and
            hasattr(self, 'current_emails') and
            self.current_emails and
            len(self.filtered_emails) < len(self.current_emails) and
            hasattr(self, 'last_search_query') and
            self.last_search_query.strip()):
            has_search_results = True

        self.export_search_button.setVisible(has_search_results)

    def _export_search_results(self):
        """Export current search results"""
        if not hasattr(self, 'filtered_emails') or not self.filtered_emails:
            QMessageBox.information(self, "Export Search Results", "No search results to export.")
            return

        query = self.last_search_query.strip()
        result_count = len(self.filtered_emails)
        title = f"Export Search Results ({result_count} emails)"
        if query:
            title += f" - '{query}'"

        dialog = ExportDialog(self.filtered_emails, title, self)
        dialog.exec()

    def _open_mbox_in_new_tab(self):
        """Open mbox file in new tab (placeholder for now)"""
        # For now, just open in current window
        self._open_mbox_file()

    def _close_mbox(self):
        """Close the current mbox file"""
        # Cancel any running operations
        self._cancel_current_search()

        if self.load_thread and self.load_thread.isRunning():
            self.load_thread.quit()
            self.load_thread.wait()

        # Close parser
        if self.current_parser:
            self.current_parser.close()
            self.current_parser = None

        # Clear data
        self.current_emails = []
        self.filtered_emails = []
        self.last_search_query = ""

        # Clear UI
        self.email_list.set_emails([])
        self.email_viewer._show_empty_state()
        self.search_input.clear()

        # Update UI state
        self._update_ui_state()
        self._update_export_search_button()

        # Update status
        self.status_label.setText("Ready")
        self.email_count_label.setText("No emails loaded")
        self.progress_bar.setVisible(False)

        # Update window title
        self.setWindowTitle("Advanced Mbox Reader")
    
    def _restore_geometry(self):
        """Restore window geometry from config"""
        geometry = self.config.get("window_geometry")
        if geometry:
            try:
                # Convert base64 string back to QByteArray
                from PySide6.QtCore import QByteArray
                geometry_bytes = QByteArray.fromBase64(geometry.encode('utf-8'))
                self.restoreGeometry(geometry_bytes)
            except Exception:
                pass  # Ignore geometry restoration errors

        state = self.config.get("window_state")
        if state:
            try:
                from PySide6.QtCore import QByteArray
                state_bytes = QByteArray.fromBase64(state.encode('utf-8'))
                self.restoreState(state_bytes)
            except Exception:
                pass  # Ignore state restoration errors
    
    def _save_geometry(self):
        """Save window geometry to config"""
        # Convert QByteArray to base64 string for JSON serialization
        geometry = self.saveGeometry()
        state = self.saveState()

        if geometry:
            self.config.set("window_geometry", geometry.toBase64().data().decode('utf-8'))
        if state:
            self.config.set("window_state", state.toBase64().data().decode('utf-8'))

        self.config.save()
    
    def _update_recent_files_menu(self):
        """Update recent files menu"""
        self.recent_menu.clear()
        
        recent_files = self.config.get("recent_files", [])
        for filepath in recent_files:
            if os.path.exists(filepath):
                action = QAction(os.path.basename(filepath), self)
                action.setToolTip(filepath)
                action.triggered.connect(lambda checked, path=filepath: self._load_mbox_file(path))
                self.recent_menu.addAction(action)
        
        if not recent_files:
            no_recent_action = QAction("No recent files", self)
            no_recent_action.setEnabled(False)
            self.recent_menu.addAction(no_recent_action)
    
    def _open_mbox_file(self):
        """Open mbox file dialog"""
        filepath, _ = QFileDialog.getOpenFileName(
            self,
            "Open Mbox File",
            "",
            "Mbox Files (*.mbox);;All Files (*)"
        )
        
        if filepath:
            self._load_mbox_file(filepath)
    
    def _load_mbox_file(self, filepath: str):
        """Load mbox file in background thread"""
        if self.load_thread and self.load_thread.isRunning():
            QMessageBox.warning(self, "Loading", "Another file is currently being loaded. Please wait.")
            return
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("Loading...")
        
        # Start loading thread
        self.load_thread = MboxLoadThread(filepath, self.config.cache_directory)
        self.load_thread.progress_updated.connect(self._on_load_progress)
        self.load_thread.emails_loaded.connect(self._on_emails_loaded)
        self.load_thread.error_occurred.connect(self._on_load_error)
        self.load_thread.start()
    
    def _on_load_progress(self, progress: int, message: str):
        """Handle load progress update"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def _on_emails_loaded(self, emails: list):
        """Handle emails loaded successfully"""
        self.current_emails = emails
        self.filtered_emails = emails  # Initialize filtered emails
        self.current_parser = self.load_thread.get_parser()

        # Clear search cache for new email set
        self.search_engine.clear_cache()

        # Clear any existing search
        self.search_input.clear()
        self.last_search_query = ""

        # Update UI
        self.email_list.set_emails(emails)
        self._update_email_count()
        self._update_ui_state()  # Enable UI elements now that emails are loaded

        # Update window title with filename
        if hasattr(self.load_thread, 'filepath'):
            filename = Path(self.load_thread.filepath).name
            self.setWindowTitle(f"Advanced Mbox Reader - {filename}")

        # Add to recent files
        self.config.add_recent_file(str(self.current_parser.filepath))
        self._update_recent_files_menu()

        # Hide progress
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready")

        # Update window title
        filename = os.path.basename(str(self.current_parser.filepath))
        self.setWindowTitle(f"Advanced Mbox Reader - {filename}")
    
    def _on_load_error(self, error_message: str):
        """Handle load error"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Error")
        
        QMessageBox.critical(self, "Error Loading Mbox", f"Failed to load mbox file:\n{error_message}")
    
    def _on_email_selected(self, email: EmailModel):
        """Handle email selection"""
        if email:
            # Enable email viewer tabs now that an email is selected
            self._update_email_viewer_tabs(True)
            self.email_viewer.display_email(email)
        else:
            # Disable email viewer tabs when no email is selected
            self._update_email_viewer_tabs(False)
    
    def _on_email_double_clicked(self, email: EmailModel):
        """Handle email double click"""
        # TODO: Open email in separate window or perform other action
        pass
    
    def _on_search_text_changed(self, query: str):
        """Handle search text change with debouncing"""
        # Stop any existing timer
        self.search_timer.stop()

        # Store the query for later processing
        self.last_search_query = query

        # If query is empty, search immediately
        if not query.strip():
            self._perform_search()
        else:
            # Start timer for debounced search
            self.search_timer.start(self.search_delay)

    def _perform_search(self):
        """Start asynchronous search operation"""
        query = self.last_search_query.strip()

        if not self.current_emails:
            return

        # Cancel any existing search
        self._cancel_current_search()

        # Handle empty query immediately (no threading needed)
        if not query:
            self.filtered_emails = self.current_emails
            # Use instant show all instead of rebuilding
            self._show_all_items_instantly()
            self.status_label.setText("Ready")
            self._update_email_count()
            self.is_showing_all_emails = True
            return

        # Start async search for non-empty queries
        self.search_thread = AsyncSearchThread(self.search_engine, self.current_emails, query)
        self.search_thread.search_completed.connect(self._on_search_completed)
        self.search_thread.search_progress.connect(self._on_search_progress)
        self.search_thread.finished.connect(self._on_search_finished)

        # Show immediate feedback
        self.status_label.setText("Searching...")

        # Start the search
        self.search_thread.start()

    def _cancel_current_search(self):
        """Cancel any running search thread"""
        if self.search_thread and self.search_thread.isRunning():
            self.search_thread.stop()
            self.search_thread.wait(1000)  # Wait up to 1 second
            if self.search_thread.isRunning():
                self.search_thread.terminate()
                self.search_thread.wait()

    def _on_search_completed(self, results, query):
        """Handle search completion"""
        # Only update if this is still the current query
        if query == self.last_search_query.strip():
            self.filtered_emails = results

            # Use ultra-fast hide/show filtering instead of rebuilding
            self.email_list.filter_emails_by_hiding(results)

            # Update showing all emails flag
            self.is_showing_all_emails = (len(results) == len(self.current_emails))

            # Update status
            result_count = len(results)
            total_count = len(self.current_emails)
            if result_count < total_count:
                self.status_label.setText(f"Found {result_count} of {total_count} emails")
            else:
                self.status_label.setText(f"{total_count} emails")

            self._update_email_count()
            self._update_export_search_button()

    def _on_search_progress(self, message):
        """Handle search progress updates"""
        self.status_label.setText(message)

    def _on_search_finished(self):
        """Handle search thread finished"""
        self.search_thread = None

    def _clear_search(self):
        """Clear search input and results immediately - ultra fast version"""
        # Cancel any running search
        self._cancel_current_search()

        # Stop any pending search timer
        self.search_timer.stop()

        # Clear input and reset state
        self.search_input.clear()
        self.last_search_query = ""

        # Check if we're already showing all emails - if so, do nothing
        if self.is_showing_all_emails:
            self.status_label.setText("Ready")
            return

        # INSTANT approach: Just show all items without rebuilding
        self._show_all_items_instantly()

        self.filtered_emails = self.current_emails
        self.status_label.setText("Ready")
        self._update_email_count()
        self._update_export_search_button()  # Hide export button
        self.is_showing_all_emails = True

    def _show_all_items_instantly(self):
        """Show all list items instantly without rebuilding"""
        # Make all items visible
        for i in range(self.email_list.count()):
            item = self.email_list.item(i)
            if item:
                item.setHidden(False)

        # Update internal state
        if hasattr(self.email_list, '_filtered_emails'):
            self.email_list._filtered_emails = self.current_emails.copy()

    def _focus_search(self):
        """Focus the search input"""
        self.search_input.setFocus()
        self.search_input.selectAll()

    def _show_advanced_search(self):
        """Show advanced search dialog"""
        if not self.current_emails:
            QMessageBox.information(self, "Advanced Search", "No emails loaded. Please open an mbox file first.")
            return

        # Get search history
        history = [criteria.query for criteria in self.search_engine.search_history if criteria.query]

        dialog = AdvancedSearchDialog(history, self)
        dialog.search_requested.connect(self._perform_advanced_search)
        dialog.exec()

    def _perform_advanced_search(self, criteria: SearchCriteria):
        """Perform advanced search with given criteria"""
        try:
            self.filtered_emails = self.search_engine.search(self.current_emails, criteria)
            self.email_list.set_emails(self.filtered_emails)
            self._update_email_count()

            # Update search input to show query
            if criteria.query:
                self.search_input.setText(criteria.query)

            # Show results in status bar
            result_count = len(self.filtered_emails)
            total_count = len(self.current_emails)
            self.status_label.setText(f"Advanced search: {result_count} of {total_count} emails")

        except Exception as e:
            QMessageBox.warning(self, "Search Error", f"Search failed: {str(e)}")
            print(f"Advanced search error: {e}")

    def _show_attachment_manager(self):
        """Show attachment manager dialog"""
        if not self.current_emails:
            QMessageBox.information(self, "Attachment Manager", "No emails loaded. Please open an mbox file first.")
            return

        # Use filtered emails if available, otherwise all emails
        emails_to_analyze = self.filtered_emails if hasattr(self, 'filtered_emails') and self.filtered_emails else self.current_emails

        # Check if any emails have attachments
        emails_with_attachments = [email for email in emails_to_analyze if email.has_attachments]

        if not emails_with_attachments:
            QMessageBox.information(
                self, "Attachment Manager",
                f"No attachments found in the {'filtered' if emails_to_analyze != self.current_emails else 'loaded'} emails."
            )
            return

        dialog = AttachmentManagerDialog(emails_to_analyze, self)
        dialog.exec()

    def _update_email_count(self):
        """Update email count in status bar"""
        if not self.current_emails:
            self.email_count_label.setText("No emails loaded")
            return

        total_count = len(self.current_emails)

        # Check if we have filtered results from advanced search
        if hasattr(self, 'filtered_emails') and self.filtered_emails is not None:
            filtered_count = len(self.filtered_emails)
        else:
            filtered_count = self.email_list.get_filtered_count()

        if filtered_count == total_count:
            self.email_count_label.setText(f"{total_count} emails")
        else:
            self.email_count_label.setText(f"{filtered_count} of {total_count} emails")
    
    def _refresh_current_mbox(self):
        """Refresh current mbox file"""
        if self.current_parser:
            self._load_mbox_file(str(self.current_parser.filepath))
    
    def _export_emails(self):
        """Export emails dialog"""
        if not self.current_emails:
            QMessageBox.information(self, "Export", "No emails loaded to export.")
            return

        # Get selected emails or all emails
        selected_emails = self.email_list.get_selected_emails()
        if selected_emails:
            emails_to_export = selected_emails
            if len(selected_emails) == 1:
                title = "Export Selected Email"
            else:
                title = f"Export {len(selected_emails)} Selected Emails"
        else:
            emails_to_export = self.current_emails
            title = "Export All Emails"

        # Show export dialog
        dialog = ExportDialog(emails_to_export, title, self)
        dialog.exec()
    
    def _show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Advanced Mbox Reader",
            "Advanced Mbox Reader v1.0\n\n"
            "A modern, high-performance mbox file reader and editor\n"
            "built with PySide6 and Python.\n\n"
            "© 2025 Andersen Partners"
        )

    def _set_window_icon(self):
        """Set the window icon"""
        try:
            # Try to load icon from file first
            icon_path = Path(__file__).parent.parent / "assets" / "icon.ico"
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
            else:
                # Fallback to embedded icon
                try:
                    import sys
                    sys.path.append(str(Path(__file__).parent.parent))
                    from assets.icon_data import get_icon_pixmap
                    pixmap = get_icon_pixmap()
                    if pixmap:
                        self.setWindowIcon(QIcon(pixmap))
                except ImportError:
                    pass
        except Exception as e:
            print(f"Could not load window icon: {e}")
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Stop any running search thread
        self._cancel_current_search()

        self._save_geometry()

        if self.current_parser:
            self.current_parser.close()

        if self.load_thread and self.load_thread.isRunning():
            self.load_thread.quit()
            self.load_thread.wait()

        event.accept()
