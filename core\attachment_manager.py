"""
Attachment manager with caching and export functionality
"""

import os
import hashlib
import pickle
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import threading
import time

from .email_model import EmailAttachment


class AttachmentCache:
    """Cache for email attachments with size limits and LRU eviction"""
    
    def __init__(self, cache_dir: Path, max_size_mb: int = 500):
        self.cache_dir = cache_dir
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.cache_dir.mkdir(exist_ok=True)
        
        self._cache_index = {}  # filename -> (size, last_access)
        self._lock = threading.RLock()
        self._load_cache_index()
    
    def _load_cache_index(self):
        """Load cache index from disk"""
        index_file = self.cache_dir / "cache_index.pkl"
        if index_file.exists():
            try:
                with open(index_file, 'rb') as f:
                    self._cache_index = pickle.load(f)
            except Exception:
                self._cache_index = {}
    
    def _save_cache_index(self):
        """Save cache index to disk"""
        index_file = self.cache_dir / "cache_index.pkl"
        try:
            with open(index_file, 'wb') as f:
                pickle.dump(self._cache_index, f)
        except Exception:
            pass
    
    def _get_cache_filename(self, attachment_key: str) -> str:
        """Generate cache filename for attachment"""
        return hashlib.md5(attachment_key.encode()).hexdigest()
    
    def _cleanup_cache(self):
        """Remove old cache files to stay within size limit"""
        total_size = sum(info[0] for info in self._cache_index.values())
        
        if total_size <= self.max_size_bytes:
            return
        
        # Sort by last access time (LRU)
        sorted_items = sorted(
            self._cache_index.items(),
            key=lambda x: x[1][1]  # Sort by last_access
        )
        
        # Remove oldest files until we're under the limit
        for filename, (size, _) in sorted_items:
            cache_file = self.cache_dir / filename
            if cache_file.exists():
                cache_file.unlink()
            
            del self._cache_index[filename]
            total_size -= size
            
            if total_size <= self.max_size_bytes * 0.8:  # Leave some headroom
                break
        
        self._save_cache_index()
    
    def get(self, attachment_key: str) -> Optional[bytes]:
        """Get attachment data from cache"""
        with self._lock:
            cache_filename = self._get_cache_filename(attachment_key)
            cache_file = self.cache_dir / cache_filename
            
            if not cache_file.exists():
                return None
            
            try:
                with open(cache_file, 'rb') as f:
                    data = f.read()
                
                # Update last access time
                if cache_filename in self._cache_index:
                    size, _ = self._cache_index[cache_filename]
                    self._cache_index[cache_filename] = (size, time.time())
                
                return data
            except Exception:
                return None
    
    def put(self, attachment_key: str, data: bytes):
        """Store attachment data in cache"""
        with self._lock:
            cache_filename = self._get_cache_filename(attachment_key)
            cache_file = self.cache_dir / cache_filename
            
            try:
                with open(cache_file, 'wb') as f:
                    f.write(data)
                
                # Update cache index
                self._cache_index[cache_filename] = (len(data), time.time())
                self._cleanup_cache()
                self._save_cache_index()
                
            except Exception:
                pass
    
    def clear(self):
        """Clear all cached attachments"""
        with self._lock:
            for filename in self._cache_index.keys():
                cache_file = self.cache_dir / filename
                if cache_file.exists():
                    cache_file.unlink()
            
            self._cache_index.clear()
            self._save_cache_index()


class AttachmentManager:
    """Manager for email attachments with caching and export functionality"""
    
    def __init__(self, cache_dir: Optional[Path] = None, cache_size_mb: int = 500):
        self.cache = AttachmentCache(cache_dir, cache_size_mb) if cache_dir else None
        self._lock = threading.RLock()
    
    def _get_attachment_key(self, email_id: str, attachment_filename: str) -> str:
        """Generate unique key for attachment"""
        return f"{email_id}_{attachment_filename}"
    
    def get_attachment_data(self, email_id: str, attachment: EmailAttachment) -> Optional[bytes]:
        """Get attachment data with caching"""
        if attachment.is_cached:
            return attachment.data
        
        # Try to load from cache
        if self.cache:
            attachment_key = self._get_attachment_key(email_id, attachment.filename)
            cached_data = self.cache.get(attachment_key)
            if cached_data:
                attachment.data = cached_data
                return cached_data
        
        return None
    
    def cache_attachment(self, email_id: str, attachment: EmailAttachment):
        """Cache attachment data"""
        if not self.cache or not attachment.is_cached:
            return
        
        attachment_key = self._get_attachment_key(email_id, attachment.filename)
        self.cache.put(attachment_key, attachment.data)
    
    def export_attachment(self, attachment: EmailAttachment, output_path: Path) -> bool:
        """Export attachment to file"""
        if not attachment.is_cached:
            return False
        
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'wb') as f:
                f.write(attachment.data)
            return True
        except Exception as e:
            print(f"Failed to export attachment {attachment.filename}: {e}")
            return False
    
    def export_all_attachments(self, email_id: str, attachments: List[EmailAttachment], 
                             output_dir: Path) -> List[Tuple[str, bool]]:
        """Export all attachments from an email"""
        results = []
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for attachment in attachments:
            # Sanitize filename
            safe_filename = self._sanitize_filename(attachment.filename)
            output_path = output_dir / safe_filename
            
            # Handle duplicate filenames
            counter = 1
            original_path = output_path
            while output_path.exists():
                stem = original_path.stem
                suffix = original_path.suffix
                output_path = output_dir / f"{stem}_{counter}{suffix}"
                counter += 1
            
            success = self.export_attachment(attachment, output_path)
            results.append((str(output_path), success))
        
        return results
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system operations"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    def get_attachment_preview(self, attachment: EmailAttachment, max_size: int = 1024) -> Optional[str]:
        """Get text preview of attachment if possible"""
        if not attachment.is_cached:
            return None
        
        # Only preview text files and small files
        if attachment.size > max_size:
            return None
        
        if not attachment.content_type.startswith('text/'):
            return None
        
        try:
            text = attachment.data.decode('utf-8', errors='replace')
            return text[:500]  # Limit preview length
        except Exception:
            return None
    
    def get_image_thumbnail(self, attachment: EmailAttachment, size: Tuple[int, int] = (150, 150)) -> Optional[bytes]:
        """Generate thumbnail for image attachments"""
        if not attachment.is_image or not attachment.is_cached:
            return None
        
        try:
            from PIL import Image
            import io
            
            # Open image from bytes
            image = Image.open(io.BytesIO(attachment.data))
            
            # Create thumbnail
            image.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Convert back to bytes
            output = io.BytesIO()
            format = image.format or 'JPEG'
            image.save(output, format=format)
            return output.getvalue()
            
        except Exception:
            return None
    
    def clear_cache(self):
        """Clear attachment cache"""
        if self.cache:
            self.cache.clear()
