"""
Email list widget with performance optimization and virtual scrolling
"""

from PySide6.QtWidgets import (QListWidget, QListWidgetItem, QWidget, QVBoxLayout, 
                               QHBoxLayout, QLabel, QFrame, QAbstractItemView)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QPalette, QPixmap, QIcon
from typing import List, Optional
import datetime

try:
    from ..core.email_model import EmailModel
except ImportError:
    from core.email_model import EmailModel


class EmailListItem(QWidget):
    """Custom widget for displaying email in list"""
    
    def __init__(self, email: EmailModel):
        super().__init__()
        self.email = email
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the email item UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(2)
        
        # Top row: sender and date
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # Sender
        self.sender_label = QLabel(self.email.sender)
        sender_font = QFont()
        sender_font.setBold(True)
        self.sender_label.setFont(sender_font)
        self.sender_label.setStyleSheet("color: #ffffff;")
        
        # Date
        date_text = ""
        if self.email.date:
            if self.email.date.date() == datetime.date.today():
                date_text = self.email.date.strftime("%H:%M")
            else:
                date_text = self.email.date.strftime("%d/%m/%Y")
        
        self.date_label = QLabel(date_text)
        self.date_label.setStyleSheet("color: #cccccc; font-size: 11px;")
        self.date_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(self.sender_label)
        top_layout.addStretch()
        top_layout.addWidget(self.date_label)
        
        # Subject
        self.subject_label = QLabel(self.email.subject)
        subject_font = QFont()
        subject_font.setBold(False)
        self.subject_label.setFont(subject_font)
        self.subject_label.setStyleSheet("color: #ffffff; font-size: 13px;")
        
        # Preview text
        self.preview_label = QLabel(self.email.preview_text)
        self.preview_label.setStyleSheet("color: #aaaaaa; font-size: 11px;")
        self.preview_label.setWordWrap(True)
        
        # Attachment indicator
        if self.email.has_attachments:
            attachment_layout = QHBoxLayout()
            attachment_layout.setContentsMargins(0, 0, 0, 0)
            
            attachment_icon = QLabel("📎")
            attachment_icon.setStyleSheet("color: #0078d4; font-size: 12px;")
            attachment_layout.addWidget(attachment_icon)
            attachment_layout.addStretch()
            
            layout.addLayout(attachment_layout)
        
        layout.addLayout(top_layout)
        layout.addWidget(self.subject_label)
        layout.addWidget(self.preview_label)
        
        # Add separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #3c3c3c;")
        layout.addWidget(separator)


class EmailListWidget(QListWidget):
    """High-performance email list widget with virtual scrolling"""
    
    email_selected = Signal(EmailModel)
    email_double_clicked = Signal(EmailModel)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._emails = []
        self._filtered_emails = []
        self._current_filter = ""
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """Setup the list widget"""
        self.setAlternatingRowColors(False)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)  # Allow multi-selection
        self.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # Enable smooth scrolling
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Set minimum size
        self.setMinimumWidth(300)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.itemClicked.connect(self._on_item_clicked)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def set_emails(self, emails: List[EmailModel]):
        """Set the list of emails to display with optimization"""
        self._emails = emails
        self._filtered_emails = emails.copy()

        # Create cache for fast restore operations
        self._create_items_cache()

        self._refresh_list_optimized()

    def filter_emails_by_hiding(self, filtered_emails: List[EmailModel]):
        """Ultra-fast filtering by hiding/showing items instead of rebuilding"""
        if not filtered_emails:
            return

        # Create set of filtered email IDs for fast lookup
        filtered_ids = {email.message_id for email in filtered_emails}

        # Hide/show items based on filter
        for i in range(self.count()):
            item = self.item(i)
            if item:
                email = item.data(Qt.UserRole)
                if email and email.message_id:
                    # Show if in filtered set, hide otherwise
                    should_show = email.message_id in filtered_ids
                    item.setHidden(not should_show)

        # Update internal state
        self._filtered_emails = filtered_emails.copy()

    def _refresh_list_optimized(self):
        """Refresh the list display with performance optimizations"""
        # Disable updates during bulk operations
        self.setUpdatesEnabled(False)

        try:
            # Clear existing items
            self.clear()

            # Batch add items for better performance
            items_to_add = []
            widgets_to_set = []

            for email in self._filtered_emails:
                # Create list item
                item = QListWidgetItem()
                item.setData(Qt.UserRole, email)

                # Create custom widget
                widget = EmailListItem(email)
                item.setSizeHint(widget.sizeHint())

                items_to_add.append(item)
                widgets_to_set.append((item, widget))

            # Add all items at once
            for item in items_to_add:
                self.addItem(item)

            # Set widgets after all items are added
            for item, widget in widgets_to_set:
                self.setItemWidget(item, widget)

        finally:
            # Re-enable updates
            self.setUpdatesEnabled(True)

    def _refresh_list(self):
        """Legacy refresh method - redirects to optimized version"""
        self._refresh_list_optimized()

    def restore_all_emails_fast(self):
        """Ultra-fast restore of all emails - optimized approach"""
        # If we're already showing all emails, do nothing
        if len(self._filtered_emails) == len(self._emails):
            return

        # Update filtered emails first
        self._filtered_emails = self._emails.copy()

        # Try cache approach first
        if hasattr(self, '_all_items_cache') and self._all_items_cache:
            # Disable updates for performance
            self.setUpdatesEnabled(False)

            try:
                # Clear current items
                self.clear()

                # Restore all cached items at once
                for item, widget in self._all_items_cache:
                    self.addItem(item)
                    self.setItemWidget(item, widget)

            finally:
                # Re-enable updates
                self.setUpdatesEnabled(True)
        else:
            # Fallback to optimized refresh
            self._refresh_list_optimized()

    def _create_items_cache(self):
        """Create cache of all email items for fast restore"""
        if not hasattr(self, '_all_items_cache'):
            self._all_items_cache = []

        # Only create cache if we don't have one or if emails changed
        if (not self._all_items_cache or
            len(self._all_items_cache) != len(self._emails)):

            self._all_items_cache = []

            for email in self._emails:
                # Create list item
                item = QListWidgetItem()
                item.setData(Qt.UserRole, email)

                # Create custom widget
                widget = EmailListItem(email)
                item.setSizeHint(widget.sizeHint())

                self._all_items_cache.append((item, widget))
    
    def filter_emails(self, query: str):
        """Filter emails by search query"""
        self._current_filter = query.lower()
        
        if not query:
            self._filtered_emails = self._emails.copy()
        else:
            self._filtered_emails = []
            for email in self._emails:
                if (query.lower() in email.subject.lower() or 
                    query.lower() in email.sender.lower() or
                    query.lower() in email.recipients.lower()):
                    self._filtered_emails.append(email)
        
        self._refresh_list()
    
    def get_selected_email(self) -> Optional[EmailModel]:
        """Get currently selected email"""
        current_item = self.currentItem()
        if current_item:
            return current_item.data(Qt.UserRole)
        return None

    def get_selected_emails(self) -> List[EmailModel]:
        """Get all currently selected emails"""
        selected_emails = []
        for item in self.selectedItems():
            email = item.data(Qt.UserRole)
            if email:
                selected_emails.append(email)
        return selected_emails
    
    def select_email(self, email: EmailModel):
        """Select specific email in the list"""
        for i in range(self.count()):
            item = self.item(i)
            if item.data(Qt.UserRole) == email:
                self.setCurrentItem(item)
                break
    
    def _on_item_clicked(self, item: QListWidgetItem):
        """Handle item click"""
        email = item.data(Qt.UserRole)
        if email:
            self.email_selected.emit(email)
    
    def _on_item_double_clicked(self, item: QListWidgetItem):
        """Handle item double click"""
        email = item.data(Qt.UserRole)
        if email:
            self.email_double_clicked.emit(email)
    
    def get_email_count(self) -> int:
        """Get total number of emails"""
        return len(self._emails)
    
    def get_filtered_count(self) -> int:
        """Get number of filtered emails"""
        return len(self._filtered_emails)
    
    def clear_selection(self):
        """Clear current selection"""
        self.setCurrentItem(None)
