"""
Export dialog for exporting emails in various formats
"""

import os
from pathlib import Path
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QComboBox, QCheckBox, QPushButton, QFileDialog,
                               QLineEdit, QGroupBox, QRadioButton, QButtonGroup,
                               QProgressBar, QTextEdit, QMessageBox)
from PySide6.QtCore import Qt, QThread, Signal
from typing import List

try:
    from ..core.email_model import EmailModel
    from ..core.import_export import EmailExporter, ArchiveManager
except ImportError:
    from core.email_model import EmailModel
    from core.import_export import EmailExporter, ArchiveManager


class ExportThread(QThread):
    """Thread for exporting emails in background"""

    progress_updated = Signal(int, str)
    export_completed = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, emails: List[EmailModel], output_path: Path, 
                 format: str, include_attachments: bool, create_archive: bool):
        super().__init__()
        self.emails = emails
        self.output_path = output_path
        self.format = format
        self.include_attachments = include_attachments
        self.create_archive = create_archive
    
    def run(self):
        """Export emails in background"""
        try:
            self.progress_updated.emit(10, "Initializing export...")
            
            exporter = EmailExporter()
            
            if self.create_archive:
                self.progress_updated.emit(30, "Creating archive...")
                print(f"Creating archive at: {self.output_path}")
                print(f"Number of emails: {len(self.emails)}")
                print(f"Include attachments: {self.include_attachments}")

                archive_manager = ArchiveManager()
                success = archive_manager.create_archive(
                    self.emails, self.output_path, self.include_attachments
                )

                print(f"Archive creation success: {success}")

                if success:
                    results = {
                        'total': len(self.emails),
                        'successful': len(self.emails),
                        'failed': 0,
                        'errors': [],
                        'output_path': str(self.output_path)
                    }
                    self.progress_updated.emit(100, "Archive created successfully")
                    self.export_completed.emit(results)
                else:
                    self.error_occurred.emit("Failed to create archive")
            
            elif len(self.emails) == 1:
                # Single email export
                self.progress_updated.emit(50, "Exporting email...")
                success = exporter.export_single_email(self.emails[0], self.output_path, self.format)
                
                if success:
                    results = {
                        'total': 1,
                        'successful': 1,
                        'failed': 0,
                        'errors': [],
                        'output_path': str(self.output_path)
                    }
                    self.progress_updated.emit(100, "Email exported successfully")
                    self.export_completed.emit(results)
                else:
                    self.error_occurred.emit("Failed to export email")
            
            else:
                # Multiple emails export
                if self.format == 'mbox':
                    self.progress_updated.emit(50, "Creating mbox file...")
                    success = exporter.export_to_mbox(self.emails, self.output_path)
                    
                    if success:
                        results = {
                            'total': len(self.emails),
                            'successful': len(self.emails),
                            'failed': 0,
                            'errors': [],
                            'output_path': str(self.output_path)
                        }
                        self.progress_updated.emit(100, "Mbox file created successfully")
                        self.export_completed.emit(results)
                    else:
                        self.error_occurred.emit("Failed to create mbox file")
                else:
                    # Export to directory
                    self.progress_updated.emit(30, "Exporting emails...")
                    results = exporter.export_multiple_emails(
                        self.emails, self.output_path, self.format, self.include_attachments
                    )
                    results['output_path'] = str(self.output_path)
                    
                    self.progress_updated.emit(100, f"Exported {results['successful']} emails")
                    self.export_completed.emit(results)
        
        except Exception as e:
            self.error_occurred.emit(str(e))


class ExportDialog(QDialog):
    """Dialog for configuring email export"""
    
    def __init__(self, emails: List[EmailModel], title: str = "Export Emails", parent=None):
        super().__init__(parent)
        self.emails = emails
        self.export_thread = None
        
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumSize(500, 400)
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Email count info
        info_label = QLabel(f"Exporting {len(self.emails)} email(s)")
        info_label.setStyleSheet("font-weight: bold; font-size: 12px; color: #ffffff;")
        layout.addWidget(info_label)
        
        # Export format group
        format_group = QGroupBox("Export Format")
        format_layout = QVBoxLayout(format_group)
        
        self.format_group = QButtonGroup()
        
        self.eml_radio = QRadioButton("EML Files (Email Message Format)")
        self.eml_radio.setChecked(True)
        self.format_group.addButton(self.eml_radio, 0)
        format_layout.addWidget(self.eml_radio)
        
        self.mbox_radio = QRadioButton("MBOX File (Mailbox Format)")
        self.format_group.addButton(self.mbox_radio, 1)
        format_layout.addWidget(self.mbox_radio)
        
        self.html_radio = QRadioButton("HTML Files")
        self.format_group.addButton(self.html_radio, 2)
        format_layout.addWidget(self.html_radio)
        
        self.txt_radio = QRadioButton("Text Files")
        self.format_group.addButton(self.txt_radio, 3)
        format_layout.addWidget(self.txt_radio)
        
        self.json_radio = QRadioButton("JSON Files")
        self.format_group.addButton(self.json_radio, 4)
        format_layout.addWidget(self.json_radio)
        
        layout.addWidget(format_group)
        
        # Options group
        options_group = QGroupBox("Export Options")
        options_layout = QVBoxLayout(options_group)
        
        self.include_attachments_cb = QCheckBox("Include attachments")
        self.include_attachments_cb.setChecked(True)
        options_layout.addWidget(self.include_attachments_cb)
        
        self.create_archive_cb = QCheckBox("Create compressed archive (ZIP)")
        options_layout.addWidget(self.create_archive_cb)
        
        layout.addWidget(options_group)
        
        # Output path
        path_group = QGroupBox("Output Location")
        path_layout = QVBoxLayout(path_group)
        
        path_input_layout = QHBoxLayout()
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText("Select output location...")
        path_input_layout.addWidget(self.path_input)
        
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self._browse_output_path)
        path_input_layout.addWidget(self.browse_button)
        
        path_layout.addLayout(path_input_layout)
        layout.addWidget(path_group)
        
        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status text (hidden initially)
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setVisible(False)
        layout.addWidget(self.status_text)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.export_button = QPushButton("Export")
        self.export_button.clicked.connect(self._start_export)
        button_layout.addWidget(self.export_button)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.format_group.buttonClicked.connect(self._on_format_changed)
        self.create_archive_cb.toggled.connect(self._on_archive_toggled)
    
    def _on_format_changed(self):
        """Handle format selection change"""
        # Update UI based on selected format
        selected_id = self.format_group.checkedId()
        
        # Disable archive option for single file formats when exporting multiple emails
        if len(self.emails) > 1 and selected_id == 1:  # MBOX
            self.create_archive_cb.setEnabled(False)
            self.create_archive_cb.setChecked(False)
        else:
            self.create_archive_cb.setEnabled(True)
    
    def _on_archive_toggled(self, checked: bool):
        """Handle archive option toggle"""
        if checked:
            self.include_attachments_cb.setChecked(True)
    
    def _browse_output_path(self):
        """Browse for output path"""
        selected_id = self.format_group.checkedId()
        create_archive = self.create_archive_cb.isChecked()
        
        if create_archive:
            # Archive file
            filepath, _ = QFileDialog.getSaveFileName(
                self,
                "Save Archive As",
                f"emails_export.zip",
                "ZIP Archives (*.zip)"
            )
            if filepath:
                self.path_input.setText(filepath)
        
        elif len(self.emails) == 1 or selected_id == 1:  # Single email or MBOX
            # Single file
            format_map = {0: 'eml', 1: 'mbox', 2: 'html', 3: 'txt', 4: 'json'}
            ext = format_map.get(selected_id, 'eml')
            
            if len(self.emails) == 1:
                default_name = f"{self.emails[0].subject or 'email'}.{ext}"
            else:
                default_name = f"emails.{ext}"
            
            filepath, _ = QFileDialog.getSaveFileName(
                self,
                "Save As",
                default_name,
                f"{ext.upper()} Files (*.{ext})"
            )
            if filepath:
                self.path_input.setText(filepath)
        
        else:
            # Directory for multiple files
            directory = QFileDialog.getExistingDirectory(
                self,
                "Select Export Directory"
            )
            if directory:
                self.path_input.setText(directory)
    
    def _start_export(self):
        """Start the export process"""
        output_path = self.path_input.text().strip()
        if not output_path:
            QMessageBox.warning(self, "Export", "Please select an output location.")
            return
        
        # Get export parameters
        selected_id = self.format_group.checkedId()
        format_map = {0: 'eml', 1: 'mbox', 2: 'html', 3: 'txt', 4: 'json'}
        format = format_map.get(selected_id, 'eml')
        
        include_attachments = self.include_attachments_cb.isChecked()
        create_archive = self.create_archive_cb.isChecked()
        
        # Disable UI during export
        self.export_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.status_text.setVisible(True)
        self.status_text.clear()
        
        # Start export thread
        self.export_thread = ExportThread(
            self.emails, Path(output_path), format, include_attachments, create_archive
        )
        self.export_thread.progress_updated.connect(self._on_progress_updated)
        self.export_thread.export_completed.connect(self._on_export_completed)
        self.export_thread.error_occurred.connect(self._on_export_error)
        self.export_thread.start()
    
    def _on_progress_updated(self, progress: int, message: str):
        """Handle progress update"""
        self.progress_bar.setValue(progress)
        self.status_text.append(message)
    
    def _on_export_completed(self, results: dict):
        """Handle export completion"""
        self.progress_bar.setVisible(False)
        self.export_button.setEnabled(True)
        
        # Show results
        total = results['total']
        successful = results['successful']
        failed = results['failed']
        output_path = results['output_path']
        
        message = f"Export completed!\n\n"
        message += f"Total emails: {total}\n"
        message += f"Successfully exported: {successful}\n"
        
        if failed > 0:
            message += f"Failed: {failed}\n"
            if results.get('errors'):
                message += "\nErrors:\n" + "\n".join(results['errors'][:5])
                if len(results['errors']) > 5:
                    message += f"\n... and {len(results['errors']) - 5} more errors"
        
        message += f"\n\nOutput location:\n{output_path}"
        
        QMessageBox.information(self, "Export Complete", message)
        self.accept()
    
    def _on_export_error(self, error_message: str):
        """Handle export error"""
        self.progress_bar.setVisible(False)
        self.export_button.setEnabled(True)
        
        QMessageBox.critical(self, "Export Error", f"Export failed:\n{error_message}")
    
    def closeEvent(self, event):
        """Handle dialog close"""
        if self.export_thread and self.export_thread.isRunning():
            self.export_thread.quit()
            self.export_thread.wait()
        event.accept()
