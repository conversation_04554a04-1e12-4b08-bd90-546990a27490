"""
Email viewer widget with rich content support (HTML, images, attachments)
"""

import os
import tempfile
from pathlib import Path
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                               QTextEdit, QLabel, QScrollArea, QFrame, QPushButton,
                               QListWidget, QListWidgetItem, QMessageBox, QFileDialog,
                               QSplitter, QGroupBox)
from PySide6.QtCore import Qt, Signal, QUrl, QTimer
from PySide6.QtGui import QFont, QPixmap, QDesktopServices
from PySide6.QtWebEngineWidgets import QWebEngineView
import html
import re
from typing import Optional, List

try:
    from ..core.email_model import EmailModel, EmailAttachment
    from ..core.attachment_manager import AttachmentManager
except ImportError:
    from core.email_model import EmailModel, EmailAttachment
    from core.attachment_manager import AttachmentManager


class AttachmentListWidget(QListWidget):
    """Widget for displaying email attachments"""
    
    attachment_double_clicked = Signal(EmailAttachment)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.attachments = []
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the attachment list"""
        self.setMaximumHeight(120)
        self.setAlternatingRowColors(True)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def set_attachments(self, attachments: List[EmailAttachment]):
        """Set the list of attachments"""
        self.clear()
        self.attachments = attachments
        
        for attachment in attachments:
            item = QListWidgetItem()
            
            # Format attachment info
            size_str = self._format_file_size(attachment.size)
            text = f"{attachment.filename} ({size_str}) - {attachment.content_type}"
            
            item.setText(text)
            item.setData(Qt.UserRole, attachment)
            
            # Add icon based on content type
            if attachment.is_image:
                item.setText(f"🖼️ {text}")
            elif attachment.content_type.startswith('text/'):
                item.setText(f"📄 {text}")
            elif attachment.content_type.startswith('application/pdf'):
                item.setText(f"📋 {text}")
            else:
                item.setText(f"📎 {text}")
            
            self.addItem(item)
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    
    def _on_item_double_clicked(self, item: QListWidgetItem):
        """Handle attachment double click"""
        attachment = item.data(Qt.UserRole)
        if attachment:
            self.attachment_double_clicked.emit(attachment)


class EmailHeaderWidget(QWidget):
    """Widget for displaying email headers"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the header widget"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Subject
        self.subject_label = QLabel()
        subject_font = QFont()
        subject_font.setBold(True)
        subject_font.setPointSize(14)
        self.subject_label.setFont(subject_font)
        self.subject_label.setWordWrap(True)
        self.subject_label.setStyleSheet("color: #ffffff; padding: 5px;")
        layout.addWidget(self.subject_label)
        
        # From
        self.from_label = QLabel()
        self.from_label.setStyleSheet("color: #cccccc; padding: 2px;")
        layout.addWidget(self.from_label)
        
        # To
        self.to_label = QLabel()
        self.to_label.setStyleSheet("color: #cccccc; padding: 2px;")
        self.to_label.setWordWrap(True)
        layout.addWidget(self.to_label)
        
        # Date
        self.date_label = QLabel()
        self.date_label.setStyleSheet("color: #aaaaaa; padding: 2px;")
        layout.addWidget(self.date_label)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #555555;")
        layout.addWidget(separator)
    
    def set_email(self, email: EmailModel):
        """Set email to display headers for"""
        self.subject_label.setText(email.subject or "(No Subject)")
        self.from_label.setText(f"From: {email.sender}")
        
        if email.recipients:
            self.to_label.setText(f"To: {email.recipients}")
            self.to_label.setVisible(True)
        else:
            self.to_label.setVisible(False)
        
        if email.date:
            date_str = email.date.strftime("%A, %B %d, %Y at %I:%M %p")
            self.date_label.setText(f"Date: {date_str}")
        else:
            self.date_label.setText("Date: Unknown")


class EmailViewerWidget(QWidget):
    """Main email viewer widget with tabs for different content types"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_email = None
        self.attachment_manager = AttachmentManager()
        self._temp_files = []  # Track temporary files for cleanup
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """Setup the email viewer UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Email header
        self.header_widget = EmailHeaderWidget()
        layout.addWidget(self.header_widget)
        
        # Main content area with tabs
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # HTML view tab
        self.html_view = QWebEngineView()
        self.tab_widget.addTab(self.html_view, "Rich View")
        
        # Plain text tab
        self.text_view = QTextEdit()
        self.text_view.setReadOnly(True)
        self.text_view.setFont(QFont("Consolas", 10))
        self.tab_widget.addTab(self.text_view, "Plain Text")
        
        # Raw source tab
        self.raw_view = QTextEdit()
        self.raw_view.setReadOnly(True)
        self.raw_view.setFont(QFont("Consolas", 9))
        self.tab_widget.addTab(self.raw_view, "Raw Source")
        
        # Attachments tab (only shown when there are attachments)
        self.attachments_widget = self._create_attachments_widget()
        self.attachments_tab_index = self.tab_widget.addTab(self.attachments_widget, "Attachments")
        self.tab_widget.setTabVisible(self.attachments_tab_index, False)
        
        # Apply custom styling to ensure disabled tabs look grayed out
        self.tab_widget.setStyleSheet("""
            QTabBar::tab:disabled {
                background: #2b2b2b;
                color: #666666;
                border: 1px solid #3c3c3c;
            }
            QTabBar::tab:selected:disabled {
                background: #2b2b2b;
                color: #666666;
                border: 1px solid #3c3c3c;
            }
        """)

        # Default empty state
        self._show_empty_state()

        # Disable all tabs initially
        self._set_tabs_enabled(False)
    
    def _create_attachments_widget(self) -> QWidget:
        """Create the attachments widget"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Attachments list
        self.attachment_list = AttachmentListWidget()
        layout.addWidget(self.attachment_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.save_attachment_btn = QPushButton("Save Selected")
        self.save_attachment_btn.clicked.connect(self._save_selected_attachment)
        button_layout.addWidget(self.save_attachment_btn)
        
        self.save_all_attachments_btn = QPushButton("Save All")
        self.save_all_attachments_btn.clicked.connect(self._save_all_attachments)
        button_layout.addWidget(self.save_all_attachments_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return widget
    
    def _setup_connections(self):
        """Setup signal connections"""
        self.attachment_list.attachment_double_clicked.connect(self._open_attachment)
    
    def _show_empty_state(self):
        """Show empty state when no email is selected"""
        empty_html = """
        <html>
        <head>
            <style>
                body { 
                    background-color: #2b2b2b; 
                    color: #ffffff; 
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                }
                .empty-message {
                    font-size: 18px;
                    color: #aaaaaa;
                }
            </style>
        </head>
        <body>
            <div class="empty-message">
                Select an email from the list to view its content
            </div>
        </body>
        </html>
        """
        
        self.html_view.setHtml(empty_html)
        self.text_view.clear()
        self.raw_view.clear()
        self.header_widget.setVisible(False)
        self.tab_widget.setTabVisible(self.attachments_tab_index, False)

        # Disable all tabs when showing empty state
        self._set_tabs_enabled(False)

    def _set_tabs_enabled(self, enabled: bool):
        """Enable or disable all tabs"""
        if not enabled:
            # Hide the tab bar completely when disabled
            self.tab_widget.tabBar().setVisible(False)
            # Also disable the widget
            self.tab_widget.setEnabled(False)
        else:
            # Show the tab bar when enabled
            self.tab_widget.tabBar().setVisible(True)
            # Enable the widget
            self.tab_widget.setEnabled(True)
            # Set to first tab
            self.tab_widget.setCurrentIndex(0)

    def set_tabs_enabled(self, enabled: bool):
        """Public method to enable/disable tabs"""
        self._set_tabs_enabled(enabled)
    
    def display_email(self, email: EmailModel):
        """Display an email in the viewer"""
        self.current_email = email

        # Enable tabs now that we have an email
        self._set_tabs_enabled(True)

        self.header_widget.setVisible(True)
        self.header_widget.set_email(email)

        # Load email content
        self._load_email_content()
        
        # Handle attachments
        if email.has_attachments:
            self.attachment_list.set_attachments(email.attachments)
            self.tab_widget.setTabVisible(self.attachments_tab_index, True)
            # Update tab text with count
            count = len(email.attachments)
            self.tab_widget.setTabText(self.attachments_tab_index, f"Attachments ({count})")
        else:
            self.tab_widget.setTabVisible(self.attachments_tab_index, False)
    
    def _load_email_content(self):
        """Load and display email content"""
        if not self.current_email:
            return
        
        # Load HTML content
        html_content = self.current_email.html_content
        if html_content:
            # Process HTML for dark mode and inline images
            processed_html = self._process_html_content(html_content)
            self.html_view.setHtml(processed_html)
        else:
            # Convert plain text to HTML
            text_content = self.current_email.text_content
            if text_content:
                html_content = self._text_to_html(text_content)
                self.html_view.setHtml(html_content)
            else:
                self.html_view.setHtml("<html><body><p>No content available</p></body></html>")
        
        # Load plain text content
        text_content = self.current_email.text_content
        if text_content:
            self.text_view.setPlainText(text_content)
        else:
            self.text_view.setPlainText("No plain text content available")
        
        # Load raw source
        try:
            raw_content = str(self.current_email.message)
            self.raw_view.setPlainText(raw_content)
        except Exception:
            self.raw_view.setPlainText("Raw source not available")
    
    def _process_html_content(self, html_content: str) -> str:
        """Process HTML content for better display"""
        # Wrap in a complete HTML document with dark mode styles
        processed_html = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ 
                    background-color: #2b2b2b !important; 
                    color: #ffffff !important; 
                    font-family: Arial, sans-serif;
                    line-height: 1.4;
                    margin: 10px;
                }}
                a {{ color: #4da6ff !important; }}
                table {{ border-collapse: collapse; }}
                td, th {{ padding: 5px; border: 1px solid #555555; }}
                blockquote {{ 
                    border-left: 3px solid #0078d4; 
                    padding-left: 10px; 
                    margin-left: 10px;
                    color: #cccccc;
                }}
                pre {{ 
                    background-color: #3c3c3c; 
                    padding: 10px; 
                    border-radius: 3px;
                    overflow-x: auto;
                }}
                img {{ max-width: 100%; height: auto; }}
            </style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """
        
        return processed_html
    
    def _text_to_html(self, text_content: str) -> str:
        """Convert plain text to HTML with basic formatting"""
        # Escape HTML characters
        html_content = html.escape(text_content)
        
        # Convert URLs to links
        url_pattern = re.compile(r'(https?://[^\s]+)')
        html_content = url_pattern.sub(r'<a href="\1" target="_blank">\1</a>', html_content)
        
        # Convert line breaks
        html_content = html_content.replace('\n', '<br>')
        
        # Wrap in HTML with dark mode styles
        return f"""
        <html>
        <head>
            <style>
                body {{ 
                    background-color: #2b2b2b; 
                    color: #ffffff; 
                    font-family: Arial, sans-serif;
                    line-height: 1.4;
                    margin: 10px;
                }}
                a {{ color: #4da6ff; }}
            </style>
        </head>
        <body>
            <pre style="white-space: pre-wrap; font-family: Arial, sans-serif;">{html_content}</pre>
        </body>
        </html>
        """
    
    def _open_attachment(self, attachment: EmailAttachment):
        """Open attachment with default application"""
        if not attachment.is_cached:
            QMessageBox.warning(self, "Attachment", "Attachment data is not available.")
            return
        
        try:
            # Create temporary file
            temp_dir = Path(tempfile.gettempdir()) / "mbox_reader_attachments"
            temp_dir.mkdir(exist_ok=True)
            
            temp_file = temp_dir / attachment.filename
            
            # Write attachment data
            with open(temp_file, 'wb') as f:
                f.write(attachment.data)
            
            # Track for cleanup
            self._temp_files.append(temp_file)
            
            # Open with default application
            QDesktopServices.openUrl(QUrl.fromLocalFile(str(temp_file)))
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open attachment:\n{str(e)}")
    
    def _save_selected_attachment(self):
        """Save selected attachment to file"""
        current_item = self.attachment_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "Save Attachment", "Please select an attachment to save.")
            return
        
        attachment = current_item.data(Qt.UserRole)
        if not attachment or not attachment.is_cached:
            QMessageBox.warning(self, "Save Attachment", "Attachment data is not available.")
            return
        
        # Get save location
        filepath, _ = QFileDialog.getSaveFileName(
            self,
            "Save Attachment",
            attachment.filename,
            "All Files (*)"
        )
        
        if filepath:
            try:
                with open(filepath, 'wb') as f:
                    f.write(attachment.data)
                QMessageBox.information(self, "Save Attachment", f"Attachment saved to:\n{filepath}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save attachment:\n{str(e)}")
    
    def _save_all_attachments(self):
        """Save all attachments to a directory"""
        if not self.current_email or not self.current_email.attachments:
            return
        
        # Get save directory
        directory = QFileDialog.getExistingDirectory(
            self,
            "Save All Attachments",
            ""
        )
        
        if directory:
            try:
                results = self.attachment_manager.export_all_attachments(
                    self.current_email.message_id,
                    self.current_email.attachments,
                    Path(directory)
                )
                
                success_count = sum(1 for _, success in results if success)
                total_count = len(results)
                
                QMessageBox.information(
                    self,
                    "Save Attachments",
                    f"Saved {success_count} of {total_count} attachments to:\n{directory}"
                )
                
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save attachments:\n{str(e)}")
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        for temp_file in self._temp_files:
            try:
                if temp_file.exists():
                    temp_file.unlink()
            except Exception:
                pass
        self._temp_files.clear()
    
    def closeEvent(self, event):
        """Handle widget close event"""
        self.cleanup_temp_files()
        event.accept()
