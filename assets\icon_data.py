"""
Base64 encoded icon data for Advanced Mbox Reader
Generated automatically - do not edit manually
"""

ICON_DATA_32x32 = "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABKElEQVR4nGNgGGDASI6mtLpX/3HJzWoSI8lMRmpYSoxjDlhqY+h3OH6VkZEWFmNzyHMfG7A5N9++Z1AXFgSLS245wshEa8vRzYFZDgOMtLacUPpgopfluMxlopfluMxnoqfl2OxhYhhgwIjP9+dOl1DFEiPTHpyJkoWQ5h9fPz2nxHIObj5JfPIsxBhyYNPCZ+RY7uAXL0VIDQuRiY9kBzj4xZsQUgOyl6gQYGBgICkaHPzifYlVy0KkOqJDwMEvPp1YtaQ4gKgQcPCLbyDFcqIdQI7BxAImWhk8tBwwi8RmFLUAyN4BDwEWSotSSgHjQFTHyNHOxDDAgAmZQ6/EiGwPEz5JWluO1QG0dATRrWJaOAKXeUzkaKKW5SBAt74hxQ4g1jGkhhoAuGdrVVy+//EAAAAASUVORK5CYII="

def get_icon_pixmap():
    """Get QPixmap from embedded icon data"""
    try:
        from PySide6.QtGui import QPixmap
        import base64
        
        icon_bytes = base64.b64decode(ICON_DATA_32x32)
        pixmap = QPixmap()
        pixmap.loadFromData(icon_bytes)
        return pixmap
    except ImportError:
        return None
