"""
Email data model with lazy loading and caching support
"""

import email
import email.utils
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path
import hashlib
import pickle
import threading
from email.message import EmailMessage


class EmailAttachment:
    """Represents an email attachment"""
    
    def __init__(self, filename: str, content_type: str, size: int, data: bytes = None):
        self.filename = filename
        self.content_type = content_type
        self.size = size
        self._data = data
        self._cached = data is not None
    
    @property
    def data(self) -> bytes:
        """Get attachment data (lazy loaded)"""
        return self._data
    
    @data.setter
    def data(self, value: bytes):
        """Set attachment data"""
        self._data = value
        self._cached = True
    
    @property
    def is_image(self) -> bool:
        """Check if attachment is an image"""
        return self.content_type.startswith('image/')
    
    @property
    def is_cached(self) -> bool:
        """Check if attachment data is cached"""
        return self._cached


class EmailModel:
    """Email data model with lazy loading and performance optimization"""
    
    def __init__(self, message: EmailMessage, mbox_offset: int = 0, mbox_path: str = None):
        self.message = message
        self.mbox_offset = mbox_offset
        self.mbox_path = mbox_path
        
        # Basic headers (always loaded)
        self.message_id = message.get('Message-ID', '')
        self.subject = self._decode_header(message.get('Subject', '(No Subject)'))
        self.sender = self._decode_header(message.get('From', ''))
        self.recipients = self._decode_header(message.get('To', ''))
        self.cc = self._decode_header(message.get('Cc', ''))
        self.bcc = self._decode_header(message.get('Bcc', ''))
        self.date_str = message.get('Date', '')
        self.date = self._parse_date(self.date_str)
        
        # Lazy loaded content
        self._text_content = None
        self._html_content = None
        self._attachments = None
        self._content_loaded = False
        
        # Caching
        self._cache_key = self._generate_cache_key()
    
    def _decode_header(self, header_value: str) -> str:
        """Decode email header with proper encoding handling"""
        if not header_value:
            return ''
        
        try:
            decoded_parts = email.header.decode_header(header_value)
            decoded_string = ''
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding, errors='replace')
                    else:
                        decoded_string += part.decode('utf-8', errors='replace')
                else:
                    decoded_string += part
            return decoded_string.strip()
        except Exception:
            return str(header_value)
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse email date string to datetime object"""
        if not date_str:
            return None
        
        try:
            return email.utils.parsedate_to_datetime(date_str)
        except Exception:
            return None
    
    def _generate_cache_key(self) -> str:
        """Generate unique cache key for this email"""
        key_data = f"{self.message_id}_{self.mbox_offset}_{self.mbox_path}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _load_content(self):
        """Load email content (text, HTML, attachments) lazily with thread safety"""
        if self._content_loaded:
            return

        # Use a simple lock to prevent multiple threads from loading content simultaneously
        if not hasattr(self, '_content_lock'):
            self._content_lock = threading.Lock()

        with self._content_lock:
            # Double-check after acquiring lock
            if self._content_loaded:
                return

            self._text_content = ""
            self._html_content = ""
            self._attachments = []

            try:
                # Walk through message parts
                if self.message.is_multipart():
                    for part in self.message.walk():
                        self._process_message_part(part)
                else:
                    self._process_message_part(self.message)
            except Exception as e:
                print(f"Error loading email content: {e}")
                # Set default values on error
                self._text_content = "Error loading content"
                self._html_content = ""
                self._attachments = []

            self._content_loaded = True
    
    def _process_message_part(self, part: EmailMessage):
        """Process individual message part"""
        content_type = part.get_content_type()
        content_disposition = part.get('Content-Disposition', '')

        # Handle attachments
        if 'attachment' in content_disposition:
            self._process_attachment(part)
            return

        # Handle inline content
        if content_type == 'text/plain':
            try:
                content = part.get_content()
                if isinstance(content, str):
                    self._text_content += content
                elif isinstance(content, bytes):
                    self._text_content += content.decode('utf-8', errors='replace')
            except Exception as e:
                # Fallback: try to get payload directly
                try:
                    payload = part.get_payload(decode=True)
                    if payload:
                        if isinstance(payload, bytes):
                            self._text_content += payload.decode('utf-8', errors='replace')
                        else:
                            self._text_content += str(payload)
                except Exception:
                    pass

        elif content_type == 'text/html':
            try:
                content = part.get_content()
                if isinstance(content, str):
                    self._html_content += content
                elif isinstance(content, bytes):
                    self._html_content += content.decode('utf-8', errors='replace')
            except Exception as e:
                # Fallback: try to get payload directly
                try:
                    payload = part.get_payload(decode=True)
                    if payload:
                        if isinstance(payload, bytes):
                            self._html_content += payload.decode('utf-8', errors='replace')
                        else:
                            self._html_content += str(payload)
                except Exception:
                    pass

        # Handle inline images and other media
        elif content_type.startswith('image/') and 'inline' in content_disposition:
            self._process_attachment(part, is_inline=True)
    
    def _process_attachment(self, part: EmailMessage, is_inline: bool = False):
        """Process email attachment"""
        filename = part.get_filename()
        if not filename:
            filename = f"attachment_{len(self._attachments) + 1}"
        
        content_type = part.get_content_type()
        
        try:
            data = part.get_content()
            if isinstance(data, str):
                data = data.encode('utf-8')
            elif not isinstance(data, bytes):
                data = bytes(data)
            
            size = len(data)
            attachment = EmailAttachment(filename, content_type, size, data)
            self._attachments.append(attachment)
            
        except Exception as e:
            # Create placeholder for failed attachments
            attachment = EmailAttachment(filename, content_type, 0)
            self._attachments.append(attachment)
    
    @property
    def text_content(self) -> str:
        """Get plain text content (lazy loaded)"""
        self._load_content()
        return self._text_content or ""
    
    @property
    def html_content(self) -> str:
        """Get HTML content (lazy loaded)"""
        self._load_content()
        return self._html_content or ""
    
    @property
    def attachments(self) -> List[EmailAttachment]:
        """Get list of attachments (lazy loaded)"""
        self._load_content()
        return self._attachments or []
    
    @property
    def has_attachments(self) -> bool:
        """Check if email has attachments without loading them"""
        if self._content_loaded:
            return len(self._attachments) > 0
        
        # Quick check without full content loading
        if self.message.is_multipart():
            for part in self.message.walk():
                if 'attachment' in part.get('Content-Disposition', ''):
                    return True
        return False
    
    @property
    def preview_text(self) -> str:
        """Get preview text (first 200 chars of text content)"""
        text = self.text_content
        if text:
            return text[:200].replace('\n', ' ').replace('\r', ' ').strip()
        
        # Fallback to HTML content stripped of tags
        html = self.html_content
        if html:
            from bs4 import BeautifulSoup
            try:
                soup = BeautifulSoup(html, 'html.parser')
                text = soup.get_text()
                return text[:200].replace('\n', ' ').replace('\r', ' ').strip()
            except Exception:
                pass
        
        return "(No preview available)"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert email to dictionary for caching"""
        return {
            'message_id': self.message_id,
            'subject': self.subject,
            'sender': self.sender,
            'recipients': self.recipients,
            'cc': self.cc,
            'bcc': self.bcc,
            'date_str': self.date_str,
            'date': self.date.isoformat() if self.date else None,
            'mbox_offset': self.mbox_offset,
            'mbox_path': self.mbox_path,
            'cache_key': self._cache_key
        }
    
    def __str__(self) -> str:
        return f"Email(subject='{self.subject}', sender='{self.sender}', date='{self.date}')"
    
    def __repr__(self) -> str:
        return self.__str__()
